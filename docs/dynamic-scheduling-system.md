# Dynamic Class Scheduling System

## Overview

The Dynamic Class Scheduling System provides flexible time selection for campus administrators, allowing them to schedule classes with arbitrary start and end times similar to meeting schedulers in Microsoft Teams, Outlook, or Google Calendar. This system moves away from fixed-duration time slots to support minute-level precision with real-time availability feedback.

## Key Features

### 1. Flexible Time Selection
- **Minute-level precision**: Schedule classes at any time (e.g., 9:17 AM - 10:42 AM)
- **Dynamic duration**: Classes can have any duration from 30 minutes to 8 hours
- **Time range picker**: Intuitive UI for selecting start and end times
- **Duration display**: Real-time calculation and display of class duration

### 2. Real-time Availability Checking
- **Live feedback**: Availability status updates as users select times
- **Resource-aware**: Checks classroom, lecturer, and group availability
- **Conflict detection**: Identifies and highlights scheduling conflicts
- **Visual indicators**: Color-coded availability status (available, busy, conflict)

### 3. Interactive Calendar Interface
- **Drag-and-drop creation**: Click and drag to create new class blocks
- **Move classes**: Drag existing classes to different times or days
- **Resize functionality**: Adjust class duration by dragging edges
- **Visual timeline**: Continuous time representation with 15-minute increments

### 4. Enhanced Resource Management
- **Classroom capacity**: Automatic capacity checking against group size
- **Equipment requirements**: Match classroom equipment with class needs
- **Lecturer preferences**: Consider availability preferences and teaching load
- **Group constraints**: Respect schedule preferences and break requirements

## System Architecture

### Core Components

#### 1. Time Utilities (`src/lib/time-utils.ts`)
```typescript
// Convert time string to minutes since midnight
timeToMinutes(time: string): number

// Convert minutes to time string format
minutesToTime(minutes: number): string

// Calculate duration between two times
calculateDuration(startTime: string, endTime: string): number

// Check if two time ranges overlap
timeRangesOverlap(range1: TimeRange, range2: TimeRange): boolean

// Create a time range object with validation
createTimeRange(startTime: string, endTime: string): TimeRange

// Find available time slots within constraints
findAvailableSlots(constraints: SchedulingConstraints, occupiedSlots: TimeRange[]): TimeRange[]
```

#### 2. Availability Checker (`src/lib/availability-checker.ts`)
```typescript
// Check if a classroom is available for a given time range
checkClassroomAvailability(timeRange: TimeRange, day: string, classroomId: string, ...): AvailabilityResult

// Check if a lecturer is available for a given time range
checkLecturerAvailability(timeRange: TimeRange, day: string, lecturerId: string, ...): AvailabilityResult

// Check overall availability for all resources
checkOverallAvailability(timeRange: TimeRange, day: string, classroomId: string, lecturerId: string, groupId: string, ...): OverallAvailabilityResult
```

#### 3. UI Components

##### Time Picker (`src/components/ui/time-picker.tsx`)
- **TimePicker**: Single time selection with minute-level precision
- **TimeRangePicker**: Start and end time selection with validation
- **DurationDisplay**: Shows calculated duration between times

##### Availability Indicators (`src/components/availability-indicator.tsx`)
- **AvailabilityIndicator**: Status badge with color coding
- **ResourceAvailabilityCard**: Detailed availability for individual resources
- **OverallAvailabilityDisplay**: Comprehensive availability overview

##### Dynamic Calendar (`src/components/dynamic-calendar-view.tsx`)
- **DynamicCalendarView**: Interactive calendar with drag-and-drop functionality
- **Entry creation**: Click and drag to create new class blocks
- **Entry manipulation**: Move and resize existing entries

##### Schedule Entry Dialog (`src/components/dynamic-schedule-entry-dialog.tsx`)
- **DynamicScheduleEntryDialog**: Enhanced dialog with real-time availability
- **Create mode**: Add new schedule entries with live feedback
- **Edit mode**: Modify existing entries with conflict checking

## Data Models

### Enhanced ScheduleEntry
```typescript
interface ScheduleEntry {
  id: string;
  module_id: string;
  lecturer_id: string;
  classroom_id: string;
  group_id: string;
  start_time: string; // e.g., "09:17" - supports minute-level precision
  end_time: string;   // e.g., "10:42" - supports minute-level precision
  day_of_week: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday';
  is_online: boolean;
  is_conflicted?: boolean;
  duration_minutes?: number; // Calculated duration for easier processing
  created_at?: Date;
  updated_at?: Date;
}
```

### TimeRange
```typescript
interface TimeRange {
  start_time: string;
  end_time: string;
  duration_minutes: number;
}
```

### AvailabilityStatus
```typescript
type AvailabilityStatus = 'available' | 'busy' | 'partially_available' | 'conflict';
```

## Usage Examples

### Creating a New Class
1. Switch to "Dynamic Calendar" view
2. Click and drag on an empty time slot to create a new class block
3. Fill in the class details in the dialog
4. Real-time availability feedback shows conflicts or availability
5. Adjust time if needed based on feedback
6. Save the entry

### Moving an Existing Class
1. In Dynamic Calendar view, click and drag an existing class block
2. Drop it on a new time slot or day
3. System automatically checks for conflicts
4. Confirms the move if no conflicts exist

### Editing Class Details
1. Click on any class block in the calendar
2. Edit dialog opens with current details pre-filled
3. Modify time, resources, or other details
4. Real-time availability checking provides immediate feedback
5. Save changes or delete the entry

## Configuration Options

### Time Increments
- Default: 15-minute increments
- Configurable: 5, 10, 15, 30-minute options
- Affects: Calendar grid resolution and time picker options

### Operating Hours
- Default: 8:00 AM - 6:00 PM
- Configurable: Adjust start and end times
- Affects: Available time slots and calendar display

### Duration Constraints
- Minimum: 30 minutes (configurable)
- Maximum: 8 hours (configurable)
- Affects: Time range picker validation

## Best Practices

### For Administrators
1. **Plan ahead**: Use availability checking to identify optimal time slots
2. **Consider preferences**: Respect lecturer and group scheduling preferences
3. **Check capacity**: Ensure classroom capacity matches group size
4. **Avoid conflicts**: Use real-time feedback to prevent scheduling conflicts
5. **Use equipment matching**: Select classrooms with required equipment

### For Developers
1. **Validate inputs**: Always validate time formats and ranges
2. **Handle edge cases**: Consider boundary conditions and invalid inputs
3. **Optimize performance**: Use debouncing for real-time availability checks
4. **Maintain consistency**: Keep duration calculations synchronized
5. **Test thoroughly**: Include edge cases in test coverage

## Troubleshooting

### Common Issues

#### Time Format Errors
- **Problem**: Invalid time format entered
- **Solution**: Use HH:MM format (e.g., "09:00", not "9:00")
- **Prevention**: Input validation and format hints

#### Availability Check Delays
- **Problem**: Slow real-time availability feedback
- **Solution**: Implement debouncing and caching
- **Prevention**: Optimize database queries

#### Drag and Drop Issues
- **Problem**: Calendar drag operations not working
- **Solution**: Check mouse event handlers and state management
- **Prevention**: Test across different browsers and devices

### Performance Optimization

1. **Debounce availability checks**: Prevent excessive API calls
2. **Cache results**: Store recent availability checks
3. **Optimize queries**: Use efficient database indexing
4. **Lazy loading**: Load calendar data on demand
5. **Virtual scrolling**: For large time ranges

## Future Enhancements

### Planned Features
1. **Recurring classes**: Support for weekly/monthly patterns
2. **Room booking integration**: Connect with external booking systems
3. **Mobile optimization**: Enhanced mobile interface
4. **Bulk operations**: Mass schedule updates
5. **Analytics**: Usage patterns and optimization suggestions

### API Extensions
1. **Webhook support**: Real-time notifications for changes
2. **Export functionality**: Calendar export to external systems
3. **Integration APIs**: Connect with student information systems
4. **Reporting**: Detailed scheduling analytics

## Migration Guide

### From Fixed Time Slots
1. **Data migration**: Convert existing fixed slots to flexible times
2. **UI updates**: Replace fixed slot components with dynamic ones
3. **Testing**: Verify all existing functionality works
4. **Training**: Update user documentation and training materials

### Backward Compatibility
- Legacy time slot format still supported
- Gradual migration path available
- Existing data preserved during transition
