import { Classroom, Lecturer, ClassGroup, Module, ScheduleEntry } from '@/lib/types'

export const mockClassrooms: Classroom[] = [
  {
    id: 'cr-1',
    room_number: 'A101',
    capacity: 30,
    equipment_details: 'Projector, Whiteboard',
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
  {
    id: 'cr-2',
    room_number: 'B202',
    capacity: 50,
    equipment_details: 'Smart Board, Audio System',
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
]

export const mockLecturers: Lecturer[] = [
  {
    id: 'lec-1',
    name: 'Dr. <PERSON>',
    contact_info: '<EMAIL>',
    availability_preferences: {
      preferred_days: ['Monday', 'Wednesday', 'Friday'],
      preferred_times: ['09:00', '10:00', '11:00'],
    },
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
  {
    id: 'lec-2',
    name: 'Prof. <PERSON>',
    contact_info: '<EMAIL>',
    availability_preferences: {
      preferred_days: ['Tuesday', 'Thursday'],
      preferred_times: ['14:00', '15:00', '16:00'],
    },
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
]

export const mockClassGroups: ClassGroup[] = [
  {
    id: 'cg-1',
    program_name: 'Computer Science',
    year_level: 1,
    group_size: 25,
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
  {
    id: 'cg-2',
    program_name: 'Mathematics',
    year_level: 2,
    group_size: 20,
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
]

export const mockModules: Module[] = [
  {
    id: 'mod-1',
    module_name: 'Introduction to Programming',
    required_hours: 3,
    practical_or_theory: 'Practical',
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
  {
    id: 'mod-2',
    module_name: 'Calculus I',
    required_hours: 4,
    practical_or_theory: 'Theory',
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
]

export const mockScheduleEntries: ScheduleEntry[] = [
  {
    id: 'se-1',
    module_id: 'mod-1',
    lecturer_id: 'lec-1',
    classroom_id: 'cr-1',
    group_id: 'cg-1',
    start_time: '09:00',
    end_time: '10:00',
    day_of_week: 'Monday',
    is_online: false,
    is_conflicted: false,
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
  {
    id: 'se-2',
    module_id: 'mod-2',
    lecturer_id: 'lec-2',
    classroom_id: 'cr-2',
    group_id: 'cg-2',
    start_time: '14:00',
    end_time: '15:00',
    day_of_week: 'Tuesday',
    is_online: true,
    is_conflicted: false,
    created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  },
]

// Conflicting schedule entry for testing conflict detection
export const mockConflictingScheduleEntry: ScheduleEntry = {
  id: 'se-conflict',
  module_id: 'mod-1',
  lecturer_id: 'lec-1', // Same lecturer as se-1
  classroom_id: 'cr-2',
  group_id: 'cg-2',
  start_time: '09:00', // Same time as se-1
  end_time: '10:00',
  day_of_week: 'Monday', // Same day as se-1
  is_online: false,
  is_conflicted: true,
  created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
  updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
}
