"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import Timetable from '@/components/timetable';
import ConflictResolver from '@/components/conflict-resolver';
import { ScheduleEntry, Classroom, Lecturer, ClassGroup, Module, TimeRange } from '@/lib/types';
import { getScheduleEntries, getClassrooms, getLecturers, getClassGroups, getModules, seedInitialData, updateScheduleEntry, createScheduleEntry, deleteScheduleEntry } from '@/lib/api-firestore';
import { PlusCircle, Trash2, CheckSquare, Square, Settings, ChevronDown, ChevronUp, Layers, Calendar } from 'lucide-react';
import NewScheduleEntryDialog from '@/components/new-schedule-entry-dialog';
import EditScheduleEntryDialog from '@/components/edit-schedule-entry-dialog';
import DynamicScheduleEntryDialog from '@/components/dynamic-schedule-entry-dialog';
import DynamicCalendarView from '@/components/dynamic-calendar-view';
import TimeSlotManager, { TimeSlot } from '@/components/time-slot-manager';
import BulkOperationsDialog from '@/components/bulk-operations-dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function DashboardPage() {
  const [scheduleEntries, setScheduleEntries] = useState<ScheduleEntry[]>([]);
  const [isConflictResolverOpen, setConflictResolverOpen] = useState(false);
  
  const [classrooms, setClassrooms] = useState<Classroom[]>([]);
  const [lecturers, setLecturers] = useState<Lecturer[]>([]);
  const [classGroups, setClassGroups] = useState<ClassGroup[]>([]);
  const [modules, setModules] = useState<Module[]>([]);

  const [isNewEntryDialogOpen, setIsNewEntryDialogOpen] = useState(false);
  const [newEntrySlot, setNewEntrySlot] = useState<{ day: string; time: string } | null>(null);

  const [isEditEntryDialogOpen, setIsEditEntryDialogOpen] = useState(false);
  const [editingEntry, setEditingEntry] = useState<ScheduleEntry | null>(null);

  const [selectedEntries, setSelectedEntries] = useState<Set<string>>(new Set());
  const [isBulkMode, setIsBulkMode] = useState(false);

  const [showTimeSlotManager, setShowTimeSlotManager] = useState(false);
  const [customTimeSlots, setCustomTimeSlots] = useState<TimeSlot[]>([]);
  const [showAvailabilityIndicators, setShowAvailabilityIndicators] = useState(true);
  const [bulkOperationsOpen, setBulkOperationsOpen] = useState(false);

  // Dynamic scheduling states
  const [isDynamicDialogOpen, setIsDynamicDialogOpen] = useState(false);
  const [dynamicDialogMode, setDynamicDialogMode] = useState<'create' | 'edit'>('create');
  const [dynamicEditingEntry, setDynamicEditingEntry] = useState<ScheduleEntry | null>(null);
  const [viewMode, setViewMode] = useState<'traditional' | 'dynamic'>('dynamic');
  
  const fetchData = async () => {
    try {
      const [
        scheduleData,
        classroomData,
        lecturerData,
        groupData,
        moduleData,
      ] = await Promise.all([
        getScheduleEntries(),
        getClassrooms(),
        getLecturers(),
        getClassGroups(),
        getModules(),
      ]);
      setScheduleEntries(scheduleData);
      setClassrooms(classroomData);
      setLecturers(lecturerData);
      setClassGroups(groupData);
      setModules(moduleData);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const handleSeedData = async () => {
    try {
      await seedInitialData();
      await fetchData();
    } catch (error) {
      console.error('Error seeding data:', error);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleAddEntry = async (entry: Omit<ScheduleEntry, 'id'>) => {
    try {
      // Create the entry in Firestore
      await createScheduleEntry({
        module_id: entry.module_id,
        lecturer_id: entry.lecturer_id,
        classroom_id: entry.classroom_id,
        group_id: entry.group_id,
        start_time: entry.start_time,
        end_time: entry.end_time,
        day_of_week: entry.day_of_week,
        is_online: entry.is_online,
      });

      // Refetch data to get the updated list with conflicts
      fetchData();
    } catch (error) {
      console.error('Error creating schedule entry:', error);
      // You could add a toast notification here
    }
  };

  const handleSlotClick = (day: string, time: string) => {
    setNewEntrySlot({ day, time });
    setIsNewEntryDialogOpen(true);
  };

  const handleEntryMove = async (entryId: string, newDay: string, newTime: string) => {
    try {
      // Calculate the new end time based on the time slot
      const timeSlots = ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'];
      const startTimeIndex = timeSlots.indexOf(newTime);
      const newEndTime = startTimeIndex !== -1 && startTimeIndex < timeSlots.length - 1
        ? timeSlots[startTimeIndex + 1]
        : '18:00';

      await updateScheduleEntry(entryId, {
        day_of_week: newDay as ScheduleEntry['day_of_week'],
        start_time: newTime,
        end_time: newEndTime,
      });

      // Refresh the data to show updated conflicts
      fetchData();
    } catch (error) {
      console.error('Error moving schedule entry:', error);
      // You could add a toast notification here
    }
  };

  const handleEntryClick = (entry: ScheduleEntry) => {
    setEditingEntry(entry);
    setIsEditEntryDialogOpen(true);
  };

  const handleUpdateEntry = async (entryId: string, updates: Partial<ScheduleEntry>) => {
    try {
      await updateScheduleEntry(entryId, updates);
      fetchData(); // Refresh data to show updated conflicts
    } catch (error) {
      console.error('Error updating schedule entry:', error);
    }
  };

  // Dynamic calendar handlers
  const handleDynamicEntryCreate = (timeRange: TimeRange, day: string) => {
    setNewEntrySlot({ day, time: timeRange.start_time });
    setDynamicDialogMode('create');
    setDynamicEditingEntry(null);
    setIsDynamicDialogOpen(true);
  };

  const handleDynamicEntryMove = async (entryId: string, newTimeRange: TimeRange, newDay: string) => {
    try {
      await updateScheduleEntry(entryId, {
        day_of_week: newDay as ScheduleEntry['day_of_week'],
        start_time: newTimeRange.start_time,
        end_time: newTimeRange.end_time,
        duration_minutes: newTimeRange.duration_minutes,
      });
      fetchData();
    } catch (error) {
      console.error('Error moving schedule entry:', error);
    }
  };

  const handleDynamicEntryResize = async (entryId: string, newTimeRange: TimeRange) => {
    try {
      await updateScheduleEntry(entryId, {
        start_time: newTimeRange.start_time,
        end_time: newTimeRange.end_time,
        duration_minutes: newTimeRange.duration_minutes,
      });
      fetchData();
    } catch (error) {
      console.error('Error resizing schedule entry:', error);
    }
  };

  const handleDynamicEntryClick = (entry: ScheduleEntry) => {
    setDynamicEditingEntry(entry);
    setDynamicDialogMode('edit');
    setIsDynamicDialogOpen(true);
  };

  const handleDynamicAddEntry = async (entry: Omit<ScheduleEntry, 'id'>) => {
    try {
      await createScheduleEntry(entry);
      fetchData();
    } catch (error) {
      console.error('Error creating schedule entry:', error);
    }
  };

  const handleDynamicUpdateEntry = async (entryId: string, updates: Partial<ScheduleEntry>) => {
    try {
      await updateScheduleEntry(entryId, updates);
      fetchData();
    } catch (error) {
      console.error('Error updating schedule entry:', error);
    }
  };

  const handleDynamicDeleteEntry = async (entryId: string) => {
    try {
      await deleteScheduleEntry(entryId);
      fetchData();
    } catch (error) {
      console.error('Error deleting schedule entry:', error);
    }
  };

  const handleDeleteEntry = async (entryId: string) => {
    try {
      await deleteScheduleEntry(entryId);
      fetchData(); // Refresh data after deletion
    } catch (error) {
      console.error('Error deleting schedule entry:', error);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedEntries.size === 0) return;

    const confirmMessage = `Are you sure you want to delete ${selectedEntries.size} schedule entries? This action cannot be undone.`;
    if (!confirm(confirmMessage)) return;

    try {
      // Delete all selected entries
      await Promise.all(
        Array.from(selectedEntries).map(entryId => deleteScheduleEntry(entryId))
      );

      setSelectedEntries(new Set());
      setIsBulkMode(false);
      fetchData(); // Refresh data after deletion
    } catch (error) {
      console.error('Error deleting schedule entries:', error);
    }
  };

  const handleEntrySelect = (entryId: string, selected: boolean) => {
    setSelectedEntries(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(entryId);
      } else {
        newSet.delete(entryId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    setSelectedEntries(new Set(scheduleEntries.map(entry => entry.id)));
  };

  const handleDeselectAll = () => {
    setSelectedEntries(new Set());
  };

  const getOccupiedTimeSlots = (): Set<string> => {
    const occupied = new Set<string>();
    scheduleEntries.forEach(entry => {
      // Map schedule entry times to time slot IDs
      // This is a simplified mapping - in a real app you'd have a more sophisticated mapping
      const timeSlotId = entry.start_time;
      occupied.add(timeSlotId);
    });
    return occupied;
  };

  const handleBulkCreate = async (entries: Omit<ScheduleEntry, 'id'>[]) => {
    try {
      const promises = entries.map(entry => createScheduleEntry(entry));
      await Promise.all(promises);
      await fetchScheduleEntries();
    } catch (error) {
      console.error('Error creating bulk entries:', error);
    }
  };

  return (
    <div className="container mx-auto py-4 sm:py-8 px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl sm:text-3xl font-headline font-bold">Weekly Timetable</h1>
        <div className="flex flex-wrap items-center gap-2 sm:gap-4 w-full sm:w-auto">
          {!isBulkMode ? (
            <>
              <Button onClick={() => setIsNewEntryDialogOpen(true)} size="sm" className="flex-1 sm:flex-none">
                <PlusCircle className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Add Entry</span>
                <span className="sm:hidden">Add</span>
              </Button>
              <Button onClick={() => setIsBulkMode(true)} variant="outline" size="sm" className="flex-1 sm:flex-none">
                <CheckSquare className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Bulk Select</span>
                <span className="sm:hidden">Select</span>
              </Button>
              <Button onClick={() => setConflictResolverOpen(true)} variant="outline" size="sm" className="flex-1 sm:flex-none">
                <span className="hidden sm:inline">Resolve Conflicts (AI)</span>
                <span className="sm:hidden">Conflicts</span>
              </Button>
              <Button
                onClick={() => setShowTimeSlotManager(!showTimeSlotManager)}
                variant="outline"
                size="sm"
                className="flex-1 sm:flex-none"
              >
                <Settings className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Time Slots</span>
                <span className="sm:hidden">Slots</span>
                {showTimeSlotManager ? <ChevronUp className="ml-1 h-3 w-3" /> : <ChevronDown className="ml-1 h-3 w-3" />}
              </Button>
              <Button
                onClick={() => setBulkOperationsOpen(true)}
                variant="outline"
                size="sm"
                className="flex-1 sm:flex-none"
              >
                <Layers className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Bulk Operations</span>
                <span className="sm:hidden">Bulk</span>
              </Button>
              {process.env.NODE_ENV === 'development' && (
                <Button onClick={handleSeedData} variant="secondary" size="sm" className="flex-1 sm:flex-none">
                  <span className="hidden sm:inline">Seed Data</span>
                  <span className="sm:hidden">Seed</span>
                </Button>
              )}
            </>
          ) : (
            <>
              <span className="text-sm text-muted-foreground whitespace-nowrap">
                {selectedEntries.size} selected
              </span>
              <Button onClick={handleSelectAll} variant="outline" size="sm" className="flex-1 sm:flex-none">
                <span className="hidden sm:inline">Select All</span>
                <span className="sm:hidden">All</span>
              </Button>
              <Button onClick={handleDeselectAll} variant="outline" size="sm" className="flex-1 sm:flex-none">
                <span className="hidden sm:inline">Deselect All</span>
                <span className="sm:hidden">None</span>
              </Button>
              <Button
                onClick={handleBulkDelete}
                variant="destructive"
                size="sm"
                disabled={selectedEntries.size === 0}
                className="flex-1 sm:flex-none"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Delete Selected</span>
                <span className="sm:hidden">Delete</span>
              </Button>
              <Button onClick={() => { setIsBulkMode(false); setSelectedEntries(new Set()); }} variant="outline" size="sm" className="flex-1 sm:flex-none">
                Cancel
              </Button>
            </>
          )}
        </div>
      </div>

      {showTimeSlotManager && (
        <div className="mb-6">
          <TimeSlotManager
            timeSlots={customTimeSlots}
            onTimeSlotsChange={setCustomTimeSlots}
            occupiedSlots={getOccupiedTimeSlots()}
          />
        </div>
      )}

      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'traditional' | 'dynamic')} className="mb-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="dynamic" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Dynamic Calendar
          </TabsTrigger>
          <TabsTrigger value="traditional" className="flex items-center gap-2">
            <Layers className="h-4 w-4" />
            Traditional Grid
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dynamic" className="mt-6">
          <DynamicCalendarView
            scheduleEntries={scheduleEntries}
            onEntryCreate={handleDynamicEntryCreate}
            onEntryMove={handleDynamicEntryMove}
            onEntryResize={handleDynamicEntryResize}
            onEntryClick={handleDynamicEntryClick}
            timeIncrement={15}
            startHour={8}
            endHour={18}
            showAvailability={showAvailabilityIndicators}
          />
        </TabsContent>

        <TabsContent value="traditional" className="mt-6">
          <Timetable
            scheduleEntries={scheduleEntries}
            onSlotClick={handleSlotClick}
            onEntryMove={handleEntryMove}
            onEntryClick={handleEntryClick}
            isBulkMode={isBulkMode}
            selectedEntries={selectedEntries}
            onEntrySelect={handleEntrySelect}
            showAvailabilityIndicators={showAvailabilityIndicators}
          />
        </TabsContent>
      </Tabs>

      <ConflictResolver
        isOpen={isConflictResolverOpen}
        onOpenChange={setConflictResolverOpen}
        scheduleEntries={scheduleEntries}
        lecturers={lecturers}
        classrooms={classrooms}
      />
      
      <NewScheduleEntryDialog
        isOpen={isNewEntryDialogOpen}
        onOpenChange={setIsNewEntryDialogOpen}
        onAddEntry={handleAddEntry}
        classrooms={classrooms}
        lecturers={lecturers}
        classGroups={classGroups}
        modules={modules}
        initialSlot={newEntrySlot}
      />

      <EditScheduleEntryDialog
        isOpen={isEditEntryDialogOpen}
        onOpenChange={setIsEditEntryDialogOpen}
        onUpdateEntry={handleUpdateEntry}
        onDeleteEntry={handleDeleteEntry}
        entry={editingEntry}
        classrooms={classrooms}
        lecturers={lecturers}
        classGroups={classGroups}
        modules={modules}
      />

      <DynamicScheduleEntryDialog
        isOpen={isDynamicDialogOpen}
        onOpenChange={setIsDynamicDialogOpen}
        onAddEntry={handleDynamicAddEntry}
        onUpdateEntry={handleDynamicUpdateEntry}
        onDeleteEntry={handleDynamicDeleteEntry}
        classrooms={classrooms}
        lecturers={lecturers}
        classGroups={classGroups}
        modules={modules}
        existingEntries={scheduleEntries}
        initialSlot={newEntrySlot}
        editingEntry={dynamicEditingEntry}
        mode={dynamicDialogMode}
      />

      <BulkOperationsDialog
        isOpen={bulkOperationsOpen}
        onOpenChange={setBulkOperationsOpen}
        scheduleEntries={scheduleEntries}
        classrooms={classrooms}
        lecturers={lecturers}
        classGroups={classGroups}
        modules={modules}
        onBulkCreate={handleBulkCreate}
      />
    </div>
  );
}
