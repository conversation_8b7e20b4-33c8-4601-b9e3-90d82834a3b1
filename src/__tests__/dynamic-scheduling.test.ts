import { describe, it, expect, beforeEach } from '@jest/globals';
import { 
  timeToMinutes, 
  minutesToTime, 
  calculateDuration, 
  timeRangesOverlap, 
  createTimeRange,
  isValidTimeFormat,
  formatDuration,
  findAvailableSlots
} from '../lib/time-utils';
import { 
  checkClassroomAvailability, 
  checkLecturerAvailability, 
  checkOverallAvailability 
} from '../lib/availability-checker';
import { ScheduleEntry, Classroom, Lecturer, ClassGroup, TimeRange } from '../lib/types';

describe('Dynamic Scheduling System', () => {
  let mockScheduleEntries: ScheduleEntry[];
  let mockClassrooms: Classroom[];
  let mockLecturers: Lecturer[];
  let mockClassGroups: ClassGroup[];

  beforeEach(() => {
    mockScheduleEntries = [
      {
        id: '1',
        module_id: 'CS101',
        lecturer_id: 'lec1',
        classroom_id: 'room1',
        group_id: 'group1',
        start_time: '09:00',
        end_time: '10:30',
        day_of_week: 'Monday',
        is_online: false,
        duration_minutes: 90
      },
      {
        id: '2',
        module_id: 'CS102',
        lecturer_id: 'lec2',
        classroom_id: 'room2',
        group_id: 'group2',
        start_time: '14:15',
        end_time: '15:45',
        day_of_week: 'Monday',
        is_online: false,
        duration_minutes: 90
      }
    ];

    mockClassrooms = [
      {
        id: 'room1',
        room_number: 'A101',
        capacity: 30,
        building: 'Main Building',
        equipment: ['projector', 'whiteboard'],
        room_type: 'lecture_hall',
        booking_restrictions: []
      },
      {
        id: 'room2',
        room_number: 'B202',
        capacity: 25,
        building: 'Science Building',
        equipment: ['computers', 'projector'],
        room_type: 'computer_lab',
        booking_restrictions: []
      }
    ];

    mockLecturers = [
      {
        id: 'lec1',
        name: 'Dr. Smith',
        email: '<EMAIL>',
        department: 'Computer Science',
        availability_preferences: {
          preferred_days: ['Monday', 'Tuesday', 'Wednesday'],
          preferred_start_time: '09:00',
          preferred_end_time: '17:00',
          max_consecutive_hours: 4,
          break_duration_minutes: 15
        },
        teaching_load: {
          max_hours_per_week: 20,
          current_hours_per_week: 8
        }
      },
      {
        id: 'lec2',
        name: 'Prof. Johnson',
        email: '<EMAIL>',
        department: 'Computer Science',
        availability_preferences: {
          preferred_days: ['Monday', 'Wednesday', 'Friday'],
          preferred_start_time: '10:00',
          preferred_end_time: '16:00',
          max_consecutive_hours: 3,
          break_duration_minutes: 30
        },
        teaching_load: {
          max_hours_per_week: 15,
          current_hours_per_week: 6
        }
      }
    ];

    mockClassGroups = [
      {
        id: 'group1',
        program_name: 'Computer Science',
        year_level: 1,
        student_count: 25,
        schedule_preferences: {
          preferred_days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday'],
          earliest_start_time: '08:00',
          latest_end_time: '18:00',
          max_consecutive_hours: 6,
          lunch_break_start: '12:00',
          lunch_break_end: '13:00'
        }
      },
      {
        id: 'group2',
        program_name: 'Information Technology',
        year_level: 2,
        student_count: 20,
        schedule_preferences: {
          preferred_days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
          earliest_start_time: '09:00',
          latest_end_time: '17:00',
          max_consecutive_hours: 4,
          lunch_break_start: '12:30',
          lunch_break_end: '13:30'
        }
      }
    ];
  });

  describe('Time Utilities', () => {
    it('should convert time to minutes correctly', () => {
      expect(timeToMinutes('09:00')).toBe(540);
      expect(timeToMinutes('14:30')).toBe(870);
      expect(timeToMinutes('00:00')).toBe(0);
      expect(timeToMinutes('23:59')).toBe(1439);
    });

    it('should convert minutes to time correctly', () => {
      expect(minutesToTime(540)).toBe('09:00');
      expect(minutesToTime(870)).toBe('14:30');
      expect(minutesToTime(0)).toBe('00:00');
      expect(minutesToTime(1439)).toBe('23:59');
    });

    it('should calculate duration correctly', () => {
      expect(calculateDuration('09:00', '10:30')).toBe(90);
      expect(calculateDuration('14:15', '15:45')).toBe(90);
      expect(calculateDuration('08:00', '18:00')).toBe(600);
    });

    it('should detect time range overlaps correctly', () => {
      const range1 = createTimeRange('09:00', '10:30');
      const range2 = createTimeRange('10:00', '11:30');
      const range3 = createTimeRange('11:00', '12:30');

      expect(timeRangesOverlap(range1, range2)).toBe(true);
      expect(timeRangesOverlap(range1, range3)).toBe(false);
      expect(timeRangesOverlap(range2, range3)).toBe(true);
    });

    it('should validate time formats correctly', () => {
      expect(isValidTimeFormat('09:00')).toBe(true);
      expect(isValidTimeFormat('14:30')).toBe(true);
      expect(isValidTimeFormat('9:00')).toBe(false);
      expect(isValidTimeFormat('25:00')).toBe(false);
      expect(isValidTimeFormat('12:60')).toBe(false);
      expect(isValidTimeFormat('abc')).toBe(false);
    });

    it('should format duration correctly', () => {
      expect(formatDuration(60)).toBe('1h');
      expect(formatDuration(90)).toBe('1h 30m');
      expect(formatDuration(30)).toBe('30m');
      expect(formatDuration(120)).toBe('2h');
    });

    it('should find available slots correctly', () => {
      const constraints = {
        min_duration_minutes: 30,
        max_duration_minutes: 240,
        time_increment_minutes: 30,
        earliest_start_time: '09:00',
        latest_end_time: '17:00'
      };

      const occupiedSlots = [
        createTimeRange('10:00', '11:30'),
        createTimeRange('14:00', '15:30')
      ];

      const availableSlots = findAvailableSlots(occupiedSlots, 90, constraints);

      expect(availableSlots.length).toBeGreaterThan(0);
      // The first available slot should be after the first occupied slot ends
      expect(availableSlots.some(slot => slot.start_time === '09:00' || slot.start_time === '11:30')).toBe(true);
    });
  });

  describe('Availability Checking', () => {
    it('should check classroom availability correctly', () => {
      const timeRange = createTimeRange('11:00', '12:30');
      const classroom = mockClassrooms.find(c => c.id === 'room1')!;

      const availability = checkClassroomAvailability(
        classroom,
        timeRange,
        'Monday',
        mockScheduleEntries
      );

      expect(availability.status).toBe('available');
      expect(availability.conflicts).toHaveLength(0);
    });

    it('should detect classroom conflicts', () => {
      const timeRange = createTimeRange('09:30', '11:00');
      const classroom = mockClassrooms.find(c => c.id === 'room1')!;

      const availability = checkClassroomAvailability(
        classroom,
        timeRange,
        'Monday',
        mockScheduleEntries
      );

      expect(availability.status).toBe('conflict');
      expect(availability.conflicts).toHaveLength(1);
    });

    it('should check lecturer availability correctly', () => {
      const timeRange = createTimeRange('11:00', '12:30');
      const lecturer = mockLecturers.find(l => l.id === 'lec1')!;

      const availability = checkLecturerAvailability(
        lecturer,
        timeRange,
        'Monday',
        mockScheduleEntries
      );

      expect(availability.status).toBe('available');
      expect(availability.conflicts).toHaveLength(0);
    });

    it('should detect lecturer conflicts', () => {
      const timeRange = createTimeRange('09:30', '11:00');
      const lecturer = mockLecturers.find(l => l.id === 'lec1')!;

      const availability = checkLecturerAvailability(
        lecturer,
        timeRange,
        'Monday',
        mockScheduleEntries
      );

      expect(availability.status).toBe('conflict');
      expect(availability.conflicts).toHaveLength(1);
    });

    it('should check overall availability correctly', () => {
      const timeRange = createTimeRange('11:00', '12:30');
      
      const availability = checkOverallAvailability(
        timeRange,
        'Monday',
        'room1',
        'lec1',
        'group1',
        mockClassrooms,
        mockLecturers,
        mockClassGroups,
        mockScheduleEntries
      );

      expect(availability.overall_status).toBe('available');
    });

    it('should detect overall conflicts', () => {
      const timeRange = createTimeRange('09:30', '11:00');
      
      const availability = checkOverallAvailability(
        timeRange,
        'Monday',
        'room1',
        'lec1',
        'group1',
        mockClassrooms,
        mockLecturers,
        mockClassGroups,
        mockScheduleEntries
      );

      expect(availability.overall_status).toBe('conflict');
    });
  });

  describe('Edge Cases', () => {
    it('should handle minute-level precision', () => {
      const timeRange = createTimeRange('09:17', '10:42');
      expect(timeRange.duration_minutes).toBe(85);
      expect(timeRange.start_time).toBe('09:17');
      expect(timeRange.end_time).toBe('10:42');
    });

    it('should handle boundary conditions', () => {
      const timeRange1 = createTimeRange('09:00', '10:00');
      const timeRange2 = createTimeRange('10:00', '11:00');
      
      // Adjacent time ranges should not overlap
      expect(timeRangesOverlap(timeRange1, timeRange2)).toBe(false);
    });

    it('should handle invalid time ranges gracefully', () => {
      expect(() => createTimeRange('10:00', '09:00')).toThrow();
      expect(() => createTimeRange('25:00', '26:00')).toThrow();
    });
  });
});
