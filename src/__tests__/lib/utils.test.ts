import { cn } from '@/lib/utils'

describe('utils', () => {
  describe('cn function', () => {
    it('should merge class names correctly', () => {
      const result = cn('px-2', 'py-1')
      expect(result).toBe('px-2 py-1')
    })

    it('should handle conditional classes', () => {
      const result = cn('base-class', true && 'conditional-class', false && 'hidden-class')
      expect(result).toBe('base-class conditional-class')
    })

    it('should handle Tailwind conflicts correctly', () => {
      const result = cn('px-2', 'px-4')
      expect(result).toBe('px-4')
    })

    it('should handle empty inputs', () => {
      const result = cn()
      expect(result).toBe('')
    })

    it('should handle undefined and null values', () => {
      const result = cn('base', undefined, null, 'end')
      expect(result).toBe('base end')
    })

    it('should handle arrays of classes', () => {
      const result = cn(['px-2', 'py-1'], 'text-center')
      expect(result).toBe('px-2 py-1 text-center')
    })

    it('should handle objects with boolean values', () => {
      const result = cn({
        'px-2': true,
        'py-1': false,
        'text-center': true,
      })
      expect(result).toBe('px-2 text-center')
    })
  })
})
