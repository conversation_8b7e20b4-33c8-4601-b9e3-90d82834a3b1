import {
  hasTimeOverlap,
  timeToMinutes,
  hasResourceConflict,
  hasScheduleConflict,
  findConflicts,
  isValidTimeFormat,
  isValidTimeRange,
  isValidDayOfWeek,
} from '@/lib/conflict-detection'
import { ScheduleEntry } from '@/lib/types'
import { mockScheduleEntries, mockConflictingScheduleEntry } from '@/test-utils/mock-data'

describe('conflict-detection', () => {
  describe('timeToMinutes', () => {
    it('should convert time string to minutes correctly', () => {
      expect(timeToMinutes('00:00')).toBe(0)
      expect(timeToMinutes('01:00')).toBe(60)
      expect(timeToMinutes('09:30')).toBe(570)
      expect(timeToMinutes('12:15')).toBe(735)
      expect(timeToMinutes('23:59')).toBe(1439)
    })
  })

  describe('hasTimeOverlap', () => {
    it('should detect overlapping time ranges', () => {
      expect(hasTimeOverlap('09:00', '10:00', '09:30', '10:30')).toBe(true)
      expect(hasTimeOverlap('09:00', '10:00', '09:00', '10:00')).toBe(true)
      expect(hasTimeOverlap('09:00', '11:00', '10:00', '12:00')).toBe(true)
    })

    it('should detect non-overlapping time ranges', () => {
      expect(hasTimeOverlap('09:00', '10:00', '10:00', '11:00')).toBe(false)
      expect(hasTimeOverlap('09:00', '10:00', '11:00', '12:00')).toBe(false)
      expect(hasTimeOverlap('14:00', '15:00', '09:00', '10:00')).toBe(false)
    })

    it('should handle edge cases', () => {
      expect(hasTimeOverlap('09:00', '10:00', '08:00', '09:00')).toBe(false)
      expect(hasTimeOverlap('09:00', '10:00', '08:30', '09:30')).toBe(true)
    })
  })

  describe('hasResourceConflict', () => {
    const baseEntry: ScheduleEntry = {
      id: 'test-1',
      module_id: 'mod-1',
      lecturer_id: 'lec-1',
      classroom_id: 'cr-1',
      group_id: 'cg-1',
      start_time: '09:00',
      end_time: '10:00',
      day_of_week: 'Monday',
      is_online: false,
      is_conflicted: false,
      created_at: { seconds: 1234567890, nanoseconds: 0 } as any,
      updated_at: { seconds: 1234567890, nanoseconds: 0 } as any,
    }

    it('should detect lecturer conflicts', () => {
      const conflictingEntry = { ...baseEntry, id: 'test-2', classroom_id: 'cr-2', group_id: 'cg-2' }
      expect(hasResourceConflict(baseEntry, conflictingEntry)).toBe(true)
    })

    it('should detect classroom conflicts for non-online classes', () => {
      const conflictingEntry = { ...baseEntry, id: 'test-2', lecturer_id: 'lec-2', group_id: 'cg-2' }
      expect(hasResourceConflict(baseEntry, conflictingEntry)).toBe(true)
    })

    it('should not detect classroom conflicts when one class is online', () => {
      const onlineEntry = { ...baseEntry, is_online: true }
      const conflictingEntry = { ...baseEntry, id: 'test-2', lecturer_id: 'lec-2', group_id: 'cg-2' }
      expect(hasResourceConflict(onlineEntry, conflictingEntry)).toBe(false)
    })

    it('should detect group conflicts', () => {
      const conflictingEntry = { ...baseEntry, id: 'test-2', lecturer_id: 'lec-2', classroom_id: 'cr-2' }
      expect(hasResourceConflict(baseEntry, conflictingEntry)).toBe(true)
    })

    it('should not detect conflicts when no resources overlap', () => {
      const nonConflictingEntry = {
        ...baseEntry,
        id: 'test-2',
        lecturer_id: 'lec-2',
        classroom_id: 'cr-2',
        group_id: 'cg-2',
      }
      expect(hasResourceConflict(baseEntry, nonConflictingEntry)).toBe(false)
    })
  })

  describe('hasScheduleConflict', () => {
    const baseEntry: ScheduleEntry = mockScheduleEntries[0]

    it('should not conflict with itself', () => {
      expect(hasScheduleConflict(baseEntry, baseEntry)).toBe(false)
    })

    it('should not conflict on different days', () => {
      const differentDayEntry = { ...baseEntry, id: 'different', day_of_week: 'Tuesday' as const }
      expect(hasScheduleConflict(baseEntry, differentDayEntry)).toBe(false)
    })

    it('should not conflict with non-overlapping times', () => {
      const laterEntry = {
        ...baseEntry,
        id: 'later',
        start_time: '11:00',
        end_time: '12:00',
        lecturer_id: 'lec-2',
        classroom_id: 'cr-2',
        group_id: 'cg-2',
      }
      expect(hasScheduleConflict(baseEntry, laterEntry)).toBe(false)
    })

    it('should detect conflicts with overlapping times and resources', () => {
      expect(hasScheduleConflict(baseEntry, mockConflictingScheduleEntry)).toBe(true)
    })
  })

  describe('findConflicts', () => {
    it('should find all conflicting entries', () => {
      const allEntries = [...mockScheduleEntries, mockConflictingScheduleEntry]
      const conflicts = findConflicts(mockScheduleEntries[0], allEntries)
      
      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].id).toBe(mockConflictingScheduleEntry.id)
    })

    it('should return empty array when no conflicts exist', () => {
      const conflicts = findConflicts(mockScheduleEntries[0], mockScheduleEntries)
      expect(conflicts).toHaveLength(0)
    })
  })

  describe('isValidTimeFormat', () => {
    it('should validate correct time formats', () => {
      expect(isValidTimeFormat('09:00')).toBe(true)
      expect(isValidTimeFormat('23:59')).toBe(true)
      expect(isValidTimeFormat('00:00')).toBe(true)
      expect(isValidTimeFormat('12:30')).toBe(true)
    })

    it('should reject invalid time formats', () => {
      expect(isValidTimeFormat('9:00')).toBe(false) // Single digit hour without leading zero
      expect(isValidTimeFormat('25:00')).toBe(false) // Invalid hour
      expect(isValidTimeFormat('12:60')).toBe(false) // Invalid minute
      expect(isValidTimeFormat('12:5')).toBe(false) // Single digit minute
      expect(isValidTimeFormat('abc')).toBe(false) // Non-numeric
      expect(isValidTimeFormat('')).toBe(false) // Empty string
    })
  })

  describe('isValidTimeRange', () => {
    it('should validate correct time ranges', () => {
      expect(isValidTimeRange('09:00', '10:00')).toBe(true)
      expect(isValidTimeRange('08:30', '09:15')).toBe(true)
      expect(isValidTimeRange('23:00', '23:59')).toBe(true)
    })

    it('should reject invalid time ranges', () => {
      expect(isValidTimeRange('10:00', '09:00')).toBe(false) // End before start
      expect(isValidTimeRange('09:00', '09:00')).toBe(false) // Same time
      expect(isValidTimeRange('invalid', '10:00')).toBe(false) // Invalid format
      expect(isValidTimeRange('09:00', 'invalid')).toBe(false) // Invalid format
    })
  })

  describe('isValidDayOfWeek', () => {
    it('should validate correct days of week', () => {
      expect(isValidDayOfWeek('Monday')).toBe(true)
      expect(isValidDayOfWeek('Tuesday')).toBe(true)
      expect(isValidDayOfWeek('Wednesday')).toBe(true)
      expect(isValidDayOfWeek('Thursday')).toBe(true)
      expect(isValidDayOfWeek('Friday')).toBe(true)
      expect(isValidDayOfWeek('Saturday')).toBe(true)
      expect(isValidDayOfWeek('Sunday')).toBe(true)
    })

    it('should reject invalid days', () => {
      expect(isValidDayOfWeek('monday')).toBe(false) // Lowercase
      expect(isValidDayOfWeek('MONDAY')).toBe(false) // Uppercase
      expect(isValidDayOfWeek('Mon')).toBe(false) // Abbreviated
      expect(isValidDayOfWeek('Invalid')).toBe(false) // Invalid day
      expect(isValidDayOfWeek('')).toBe(false) // Empty string
    })
  })
})
