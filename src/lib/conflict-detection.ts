import { ScheduleEntry, TimeRange } from './types'
import { timeToMinutes, timeRangesOverlap, createTimeRange } from './time-utils'

/**
 * Checks if two time ranges overlap (legacy function - use timeRangesOverlap from time-utils)
 * @param start1 Start time of first range (e.g., "09:00")
 * @param end1 End time of first range (e.g., "10:00")
 * @param start2 Start time of second range (e.g., "09:30")
 * @param end2 End time of second range (e.g., "10:30")
 * @returns true if the time ranges overlap
 */
export const hasTimeOverlap = (start1: string, end1: string, start2: string, end2: string): boolean => {
  const range1 = createTimeRange(start1, end1);
  const range2 = createTimeRange(start2, end2);
  return timeRangesOverlap(range1, range2);
}

// Re-export timeToMinutes from time-utils for backward compatibility
export { timeToMinutes } from './time-utils';

/**
 * Checks if two schedule entries have resource conflicts
 * @param entry1 First schedule entry
 * @param entry2 Second schedule entry
 * @returns true if there are resource conflicts
 */
export const hasResourceConflict = (entry1: ScheduleEntry, entry2: ScheduleEntry): boolean => {
  // Same lecturer cannot be in two places at once
  if (entry1.lecturer_id === entry2.lecturer_id) {
    return true
  }
  
  // Same classroom cannot host two classes at once (unless one is online)
  if (entry1.classroom_id === entry2.classroom_id && !entry1.is_online && !entry2.is_online) {
    return true
  }
  
  // Same group cannot attend two classes at once
  if (entry1.group_id === entry2.group_id) {
    return true
  }
  
  return false
}

/**
 * Checks if two schedule entries conflict with each other
 * @param entry1 First schedule entry
 * @param entry2 Second schedule entry
 * @returns true if the entries conflict
 */
export const hasScheduleConflict = (entry1: ScheduleEntry, entry2: ScheduleEntry): boolean => {
  // Same entry cannot conflict with itself
  if (entry1.id === entry2.id) {
    return false
  }
  
  // Different days cannot conflict
  if (entry1.day_of_week !== entry2.day_of_week) {
    return false
  }
  
  // Check for time overlap
  if (!hasTimeOverlap(entry1.start_time, entry1.end_time, entry2.start_time, entry2.end_time)) {
    return false
  }
  
  // If time overlaps, check for resource conflicts
  return hasResourceConflict(entry1, entry2)
}

/**
 * Finds all conflicts for a given schedule entry within a list of entries
 * @param targetEntry The entry to check for conflicts
 * @param allEntries All schedule entries to check against
 * @returns Array of conflicting entries
 */
export const findConflicts = (targetEntry: ScheduleEntry, allEntries: ScheduleEntry[]): ScheduleEntry[] => {
  return allEntries.filter(entry => hasScheduleConflict(targetEntry, entry))
}

/**
 * Validates if a time string is in correct format (HH:MM)
 * @param time Time string to validate
 * @returns true if valid format
 */
export const isValidTimeFormat = (time: string): boolean => {
  const timeRegex = /^([0-1][0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(time)
}

/**
 * Validates if end time is after start time
 * @param startTime Start time string
 * @param endTime End time string
 * @returns true if end time is after start time
 */
export const isValidTimeRange = (startTime: string, endTime: string): boolean => {
  if (!isValidTimeFormat(startTime) || !isValidTimeFormat(endTime)) {
    return false
  }
  
  return timeToMinutes(endTime) > timeToMinutes(startTime)
}

/**
 * Validates if a day of week is valid
 * @param day Day of week string
 * @returns true if valid day
 */
export const isValidDayOfWeek = (day: string): boolean => {
  const validDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
  return validDays.includes(day)
}
