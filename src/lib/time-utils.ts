import { TimeRange, SchedulingConstraints } from './types';

/**
 * Converts time string (HH:MM) to minutes since midnight
 * @param time Time string in HH:MM format
 * @returns Minutes since midnight
 */
export const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

/**
 * Converts minutes since midnight to time string (HH:MM)
 * @param minutes Minutes since midnight
 * @returns Time string in HH:MM format
 */
export const minutesToTime = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

/**
 * Calculates duration between two times in minutes
 * @param startTime Start time in HH:MM format
 * @param endTime End time in HH:MM format
 * @returns Duration in minutes
 */
export const calculateDuration = (startTime: string, endTime: string): number => {
  return timeToMinutes(endTime) - timeToMinutes(startTime);
};

/**
 * Creates a TimeRange object from start and end times
 * @param startTime Start time in HH:MM format
 * @param endTime End time in HH:MM format
 * @returns TimeRange object
 * @throws Error if time format is invalid or end time is before start time
 */
export const createTimeRange = (startTime: string, endTime: string): TimeRange => {
  if (!isValidTimeFormat(startTime) || !isValidTimeFormat(endTime)) {
    throw new Error('Invalid time format. Use HH:MM format.');
  }

  const duration_minutes = calculateDuration(startTime, endTime);

  if (duration_minutes <= 0) {
    throw new Error('End time must be after start time.');
  }

  return {
    start_time: startTime,
    end_time: endTime,
    duration_minutes
  };
};

/**
 * Checks if two time ranges overlap
 * @param range1 First time range
 * @param range2 Second time range
 * @returns True if ranges overlap
 */
export const timeRangesOverlap = (range1: TimeRange, range2: TimeRange): boolean => {
  const start1 = timeToMinutes(range1.start_time);
  const end1 = timeToMinutes(range1.end_time);
  const start2 = timeToMinutes(range2.start_time);
  const end2 = timeToMinutes(range2.end_time);
  
  return start1 < end2 && end1 > start2;
};

/**
 * Validates if a time string is in correct format (HH:MM)
 * @param time Time string to validate
 * @returns True if valid format
 */
export const isValidTimeFormat = (time: string): boolean => {
  const timeRegex = /^([0-1][0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
};

/**
 * Validates if a time range is valid (start < end, valid format)
 * @param timeRange Time range to validate
 * @returns True if valid
 */
export const isValidTimeRange = (timeRange: TimeRange): boolean => {
  if (!isValidTimeFormat(timeRange.start_time) || !isValidTimeFormat(timeRange.end_time)) {
    return false;
  }
  
  return timeToMinutes(timeRange.start_time) < timeToMinutes(timeRange.end_time);
};

/**
 * Rounds time to nearest increment (e.g., 15-minute intervals)
 * @param time Time string in HH:MM format
 * @param incrementMinutes Increment in minutes (default: 15)
 * @returns Rounded time string
 */
export const roundTimeToIncrement = (time: string, incrementMinutes: number = 15): string => {
  const totalMinutes = timeToMinutes(time);
  const roundedMinutes = Math.round(totalMinutes / incrementMinutes) * incrementMinutes;
  return minutesToTime(roundedMinutes);
};

/**
 * Generates time slots for a given day based on constraints
 * @param constraints Scheduling constraints
 * @returns Array of available time slots
 */
export const generateTimeSlots = (constraints: SchedulingConstraints): string[] => {
  const slots: string[] = [];
  const startMinutes = timeToMinutes(constraints.earliest_start_time);
  const endMinutes = timeToMinutes(constraints.latest_end_time);
  const increment = constraints.time_increment_minutes;
  
  for (let minutes = startMinutes; minutes < endMinutes; minutes += increment) {
    slots.push(minutesToTime(minutes));
  }
  
  return slots;
};

/**
 * Finds available time slots of specified duration
 * @param occupiedRanges Array of occupied time ranges
 * @param requiredDuration Required duration in minutes
 * @param constraints Scheduling constraints
 * @returns Array of available time ranges
 */
export const findAvailableSlots = (
  occupiedRanges: TimeRange[],
  requiredDuration: number,
  constraints: SchedulingConstraints
): TimeRange[] => {
  const availableSlots: TimeRange[] = [];
  const startMinutes = timeToMinutes(constraints.earliest_start_time);
  const endMinutes = timeToMinutes(constraints.latest_end_time);
  const increment = constraints.time_increment_minutes;
  
  // Sort occupied ranges by start time
  const sortedOccupied = [...occupiedRanges].sort((a, b) => 
    timeToMinutes(a.start_time) - timeToMinutes(b.start_time)
  );
  
  let currentTime = startMinutes;
  
  for (const occupied of sortedOccupied) {
    const occupiedStart = timeToMinutes(occupied.start_time);
    
    // Check if there's a gap before this occupied slot
    if (currentTime + requiredDuration <= occupiedStart) {
      // Find all possible slots in this gap
      while (currentTime + requiredDuration <= occupiedStart) {
        const slotEnd = currentTime + requiredDuration;
        if (slotEnd <= endMinutes) {
          availableSlots.push(createTimeRange(
            minutesToTime(currentTime),
            minutesToTime(slotEnd)
          ));
        }
        currentTime += increment;
      }
    }
    
    // Move past this occupied slot
    currentTime = Math.max(currentTime, timeToMinutes(occupied.end_time));
  }
  
  // Check for slots after the last occupied range
  while (currentTime + requiredDuration <= endMinutes) {
    availableSlots.push(createTimeRange(
      minutesToTime(currentTime),
      minutesToTime(currentTime + requiredDuration)
    ));
    currentTime += increment;
  }
  
  return availableSlots;
};

/**
 * Formats duration in minutes to human-readable string
 * @param minutes Duration in minutes
 * @returns Formatted string (e.g., "1h 30m")
 */
export const formatDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours === 0) {
    return `${mins}m`;
  } else if (mins === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${mins}m`;
  }
};

/**
 * Parses time range string (e.g., "09:00-12:00") to TimeRange object
 * @param rangeString Time range string
 * @returns TimeRange object or null if invalid
 */
export const parseTimeRangeString = (rangeString: string): TimeRange | null => {
  const parts = rangeString.split('-');
  if (parts.length !== 2) return null;
  
  const [startTime, endTime] = parts.map(t => t.trim());
  if (!isValidTimeFormat(startTime) || !isValidTimeFormat(endTime)) return null;
  
  const timeRange = createTimeRange(startTime, endTime);
  return isValidTimeRange(timeRange) ? timeRange : null;
};
