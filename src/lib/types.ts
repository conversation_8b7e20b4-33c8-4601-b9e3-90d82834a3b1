export interface Classroom {
  id: string;
  room_number: string;
  capacity: number;
  equipment_details: string;
  equipment_list?: string[]; // Structured list of equipment
  room_type?: 'lecture_hall' | 'laboratory' | 'computer_lab' | 'seminar_room' | 'workshop';
  accessibility_features?: string[];
  booking_restrictions?: {
    min_booking_duration?: number; // in minutes
    max_booking_duration?: number; // in minutes
    requires_approval?: boolean;
  };
  created_at?: Date;
  updated_at?: Date;
}

export interface Lecturer {
  id: string;
  name: string;
  contact_info: string;
  availability_preferences: {
    preferred_days: string[];
    preferred_times: string[]; // Time ranges like "09:00-12:00"
    blocked_times?: string[]; // Unavailable time ranges
    max_consecutive_hours?: number;
    max_daily_hours?: number;
    preferred_break_duration?: number; // in minutes
  };
  // Keep the old format for backward compatibility during migration
  availability_preferences_json?: string;
  teaching_load?: {
    current_weekly_hours: number;
    max_weekly_hours: number;
    preferred_weekly_hours?: number;
  };
  specializations?: string[]; // Subject areas or equipment they can use
  created_at?: Date;
  updated_at?: Date;
}

export interface ClassGroup {
  id: string;
  program_name: string;
  year_level: number;
  group_size: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface Module {
  id: string;
  module_name: string;
  required_hours: number;
  practical_or_theory: 'Practical' | 'Theory';
  created_at?: Date;
  updated_at?: Date;
}

export interface ScheduleEntry {
  id: string;
  module_id: string;
  lecturer_id: string;
  classroom_id: string;
  group_id: string;
  start_time: string; // e.g., "09:17" - supports minute-level precision
  end_time: string; // e.g., "10:42" - supports minute-level precision
  day_of_week: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday';
  is_online: boolean;
  is_conflicted?: boolean; // Optional: for UI highlighting
  duration_minutes?: number; // Calculated duration for easier processing
  created_at?: Date;
  updated_at?: Date;
}

// Availability and Resource Management Types
export type AvailabilityStatus = 'available' | 'busy' | 'partially_available' | 'conflict';

export interface TimeRange {
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  duration_minutes: number;
}

export interface AvailabilitySlot {
  time_range: TimeRange;
  status: AvailabilityStatus;
  conflicts?: ScheduleEntry[]; // Conflicting entries if any
  resource_type: 'classroom' | 'lecturer' | 'group' | 'equipment';
  resource_id: string;
}

export interface ResourceAvailability {
  resource_id: string;
  resource_type: 'classroom' | 'lecturer' | 'group' | 'equipment';
  day_of_week: string;
  availability_slots: AvailabilitySlot[];
  preferences?: {
    preferred_times?: string[]; // Array of time ranges like ["09:00-12:00", "14:00-17:00"]
    blocked_times?: string[]; // Times when resource is unavailable
    max_consecutive_hours?: number;
  };
}

export interface SchedulingConstraints {
  min_duration_minutes: number;
  max_duration_minutes: number;
  time_increment_minutes: number; // e.g., 15 for quarter-hour increments
  earliest_start_time: string;
  latest_end_time: string;
  break_duration_minutes?: number;
}

export interface DynamicScheduleEntry extends Omit<ScheduleEntry, 'start_time' | 'end_time'> {
  time_range: TimeRange;
  availability_status?: AvailabilityStatus;
  suggested_alternatives?: TimeRange[];
}

// Firestore-specific types for data operations
export interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

// Helper types for form data (without timestamps)
export type ClassroomFormData = Omit<Classroom, 'id' | 'created_at' | 'updated_at'>;
export type LecturerFormData = Omit<Lecturer, 'id' | 'created_at' | 'updated_at' | 'availability_preferences_json'>;
export type ClassGroupFormData = Omit<ClassGroup, 'id' | 'created_at' | 'updated_at'>;
export type ModuleFormData = Omit<Module, 'id' | 'created_at' | 'updated_at'>;
export type ScheduleEntryFormData = Omit<ScheduleEntry, 'id' | 'created_at' | 'updated_at' | 'is_conflicted'>;
