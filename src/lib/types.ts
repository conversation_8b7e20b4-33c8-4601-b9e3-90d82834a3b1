// Equipment types for structured equipment management
export interface Equipment {
  id: string;
  name: string;
  type: 'projector' | 'computer' | 'whiteboard' | 'smartboard' | 'audio_system' | 'lab_equipment' | 'specialized_software' | 'other';
  quantity: number;
  is_working: boolean;
  requires_training?: boolean;
  maintenance_schedule?: {
    last_maintenance: Date;
    next_maintenance: Date;
    maintenance_notes?: string;
  };
  booking_restrictions?: {
    requires_approval: boolean;
    max_booking_duration?: number; // in minutes
    advance_booking_required?: number; // hours in advance
  };
}

export interface Classroom {
  id: string;
  room_number: string;
  capacity: number;
  equipment_details: string; // Keep for backward compatibility
  equipment_list: Equipment[]; // Enhanced structured equipment list
  room_type: 'lecture_hall' | 'laboratory' | 'computer_lab' | 'seminar_room' | 'workshop' | 'auditorium' | 'meeting_room';
  accessibility_features: string[];
  booking_restrictions: {
    min_booking_duration: number; // in minutes (default: 30)
    max_booking_duration: number; // in minutes (default: 480 = 8 hours)
    requires_approval: boolean;
    advance_booking_required?: number; // hours in advance
    restricted_times?: string[]; // Time ranges when room is unavailable
    max_daily_bookings?: number;
  };
  location?: {
    building: string;
    floor: number;
    wing?: string;
  };
  maintenance_schedule?: {
    regular_maintenance_day?: string; // Day of week
    regular_maintenance_time?: string; // Time range like "18:00-20:00"
    next_maintenance?: Date;
    maintenance_notes?: string;
  };
  utilization_stats?: {
    weekly_usage_hours: number;
    peak_usage_times: string[]; // Most commonly booked time ranges
    average_occupancy_rate: number; // Percentage
  };
  created_at?: Date;
  updated_at?: Date;
}

export interface Lecturer {
  id: string;
  name: string;
  contact_info: string;
  availability_preferences: {
    preferred_days: string[];
    preferred_times: string[]; // Time ranges like "09:00-12:00"
    blocked_times: string[]; // Unavailable time ranges
    max_consecutive_hours: number; // Maximum consecutive teaching hours
    max_daily_hours: number; // Maximum teaching hours per day
    preferred_break_duration: number; // Minimum break between classes (minutes)
    lunch_break?: {
      start_time: string; // e.g., "12:00"
      duration: number; // in minutes
      is_flexible: boolean; // Can be moved if needed
    };
    weekly_preferences?: {
      [key: string]: { // Day of week
        preferred_start_time?: string;
        preferred_end_time?: string;
        max_hours_per_day?: number;
        blocked_periods?: string[]; // Specific blocked time ranges for this day
      };
    };
    semester_preferences?: {
      preferred_total_hours: number;
      max_courses: number;
      preferred_course_types?: ('theory' | 'practical' | 'lab' | 'seminar')[];
      research_time_blocks?: string[]; // Protected time for research
    };
  };
  // Keep the old format for backward compatibility during migration
  availability_preferences_json?: string;
  teaching_load: {
    current_weekly_hours: number;
    max_weekly_hours: number;
    preferred_weekly_hours: number;
    current_courses: number;
    max_courses: number;
    specializations: string[]; // Subject areas they can teach
    equipment_certifications: string[]; // Equipment they're certified to use
  };
  performance_metrics?: {
    average_class_rating: number;
    total_classes_taught: number;
    preferred_class_sizes: {
      min: number;
      max: number;
      optimal: number;
    };
  };
  created_at?: Date;
  updated_at?: Date;
}

export interface ClassGroup {
  id: string;
  program_name: string;
  year_level: number;
  group_size: number;
  special_requirements?: {
    accessibility_needs: string[];
    equipment_requirements: string[]; // Required equipment types
    room_type_preferences: ('lecture_hall' | 'laboratory' | 'computer_lab' | 'seminar_room' | 'workshop')[];
    min_room_capacity?: number; // Override if different from group_size
  };
  scheduling_constraints?: {
    preferred_time_blocks: string[]; // Preferred time ranges
    blocked_times: string[]; // Times when group is unavailable (e.g., other commitments)
    max_daily_hours: number; // Maximum hours per day for this group
    preferred_break_duration: number; // Minimum break between classes
    back_to_back_allowed: boolean; // Can have consecutive classes
  };
  created_at?: Date;
  updated_at?: Date;
}

export interface Module {
  id: string;
  module_name: string;
  required_hours: number;
  practical_or_theory: 'Practical' | 'Theory' | 'Lab' | 'Seminar' | 'Workshop';
  equipment_requirements: string[]; // Required equipment for this module
  room_requirements: {
    preferred_room_types: ('lecture_hall' | 'laboratory' | 'computer_lab' | 'seminar_room' | 'workshop')[];
    min_capacity?: number;
    required_equipment: string[];
    accessibility_requirements?: string[];
  };
  teaching_requirements?: {
    requires_specialist: boolean; // Needs lecturer with specific certifications
    required_certifications: string[];
    team_teaching?: boolean; // Multiple lecturers needed
    preparation_time: number; // Minutes needed before class for setup
    cleanup_time: number; // Minutes needed after class for cleanup
  };
  created_at?: Date;
  updated_at?: Date;
}

export interface ScheduleEntry {
  id: string;
  module_id: string;
  lecturer_id: string;
  classroom_id: string;
  group_id: string;
  start_time: string; // e.g., "09:17" - supports minute-level precision
  end_time: string; // e.g., "10:42" - supports minute-level precision
  day_of_week: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday';
  is_online: boolean;
  is_conflicted?: boolean; // Optional: for UI highlighting
  duration_minutes?: number; // Calculated duration for easier processing
  created_at?: Date;
  updated_at?: Date;
}

// Availability and Resource Management Types
export type AvailabilityStatus = 'available' | 'busy' | 'partially_available' | 'conflict';

export interface TimeRange {
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  duration_minutes: number;
}

export interface AvailabilitySlot {
  time_range: TimeRange;
  status: AvailabilityStatus;
  conflicts?: ScheduleEntry[]; // Conflicting entries if any
  resource_type: 'classroom' | 'lecturer' | 'group' | 'equipment';
  resource_id: string;
}

export interface ResourceAvailability {
  resource_id: string;
  resource_type: 'classroom' | 'lecturer' | 'group' | 'equipment';
  day_of_week: string;
  availability_slots: AvailabilitySlot[];
  preferences?: {
    preferred_times?: string[]; // Array of time ranges like ["09:00-12:00", "14:00-17:00"]
    blocked_times?: string[]; // Times when resource is unavailable
    max_consecutive_hours?: number;
  };
}

// Resource utilization tracking
export interface ResourceUtilization {
  resource_id: string;
  resource_type: 'classroom' | 'lecturer' | 'group' | 'equipment';
  period: 'daily' | 'weekly' | 'monthly' | 'semester';
  utilization_data: {
    total_available_hours: number;
    total_booked_hours: number;
    utilization_rate: number; // Percentage
    peak_usage_times: string[];
    underutilized_times: string[];
    average_booking_duration: number; // in minutes
  };
  efficiency_metrics?: {
    setup_time_average: number; // Average setup time in minutes
    cleanup_time_average: number; // Average cleanup time in minutes
    cancellation_rate: number; // Percentage of bookings cancelled
    no_show_rate: number; // Percentage of bookings where resource wasn't used
  };
  date_range: {
    start_date: Date;
    end_date: Date;
  };
}

// Equipment booking and tracking
export interface EquipmentBooking {
  id: string;
  equipment_id: string;
  schedule_entry_id?: string; // Optional link to class schedule
  booked_by: string; // User/lecturer ID
  booking_purpose: string;
  start_time: string;
  end_time: string;
  day_of_week: string;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  setup_requirements?: string[];
  special_instructions?: string;
  approval_required: boolean;
  approved_by?: string;
  created_at: Date;
  updated_at: Date;
}

// Resource conflict resolution
export interface ResourceConflict {
  id: string;
  conflict_type: 'double_booking' | 'capacity_exceeded' | 'equipment_unavailable' | 'lecturer_overload' | 'maintenance_conflict';
  affected_resources: {
    resource_id: string;
    resource_type: 'classroom' | 'lecturer' | 'group' | 'equipment';
    resource_name: string;
  }[];
  conflicting_entries: string[]; // Schedule entry IDs
  severity: 'low' | 'medium' | 'high' | 'critical';
  suggested_resolutions: {
    resolution_type: 'reschedule' | 'change_room' | 'change_lecturer' | 'split_class' | 'cancel';
    description: string;
    alternative_options?: {
      resource_id?: string;
      time_slot?: string;
      day_of_week?: string;
    }[];
  }[];
  status: 'unresolved' | 'resolved' | 'ignored';
  resolved_by?: string;
  resolution_notes?: string;
  created_at: Date;
  updated_at?: Date;
}

export interface SchedulingConstraints {
  min_duration_minutes: number;
  max_duration_minutes: number;
  time_increment_minutes: number; // e.g., 15 for quarter-hour increments
  earliest_start_time: string;
  latest_end_time: string;
  break_duration_minutes?: number;
}

export interface DynamicScheduleEntry extends Omit<ScheduleEntry, 'start_time' | 'end_time'> {
  time_range: TimeRange;
  availability_status?: AvailabilityStatus;
  suggested_alternatives?: TimeRange[];
}

// Firestore-specific types for data operations
export interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

// Helper types for form data (without timestamps)
export type ClassroomFormData = Omit<Classroom, 'id' | 'created_at' | 'updated_at'>;
export type LecturerFormData = Omit<Lecturer, 'id' | 'created_at' | 'updated_at' | 'availability_preferences_json'>;
export type ClassGroupFormData = Omit<ClassGroup, 'id' | 'created_at' | 'updated_at'>;
export type ModuleFormData = Omit<Module, 'id' | 'created_at' | 'updated_at'>;
export type ScheduleEntryFormData = Omit<ScheduleEntry, 'id' | 'created_at' | 'updated_at' | 'is_conflicted'>;
