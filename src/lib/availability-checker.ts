import { 
  ScheduleEntry, 
  Classroom, 
  Lecturer, 
  ClassGroup, 
  TimeRange, 
  AvailabilityStatus, 
  AvailabilitySlot, 
  ResourceAvailability 
} from './types';
import { timeRangesOverlap, parseTimeRangeString, createTimeRange } from './time-utils';

/**
 * Checks availability of a classroom for a given time range
 * @param classroom Classroom to check
 * @param timeRange Time range to check
 * @param dayOfWeek Day of the week
 * @param existingEntries Existing schedule entries
 * @param excludeEntryId Optional entry ID to exclude from conflict checking
 * @returns Availability status and conflicting entries
 */
export const checkClassroomAvailability = (
  classroom: Classroom,
  timeRange: TimeRange,
  dayOfWeek: string,
  existingEntries: ScheduleEntry[],
  excludeEntryId?: string
): { status: AvailabilityStatus; conflicts: ScheduleEntry[] } => {
  const conflicts = existingEntries.filter(entry => {
    if (entry.id === excludeEntryId) return false;
    if (entry.day_of_week !== dayOfWeek) return false;
    if (entry.classroom_id !== classroom.id) return false;
    if (entry.is_online) return false; // Online classes don't use physical rooms
    
    const entryRange = createTimeRange(entry.start_time, entry.end_time);
    return timeRangesOverlap(timeRange, entryRange);
  });
  
  const status: AvailabilityStatus = conflicts.length > 0 ? 'conflict' : 'available';
  return { status, conflicts };
};

/**
 * Checks availability of a lecturer for a given time range
 * @param lecturer Lecturer to check
 * @param timeRange Time range to check
 * @param dayOfWeek Day of the week
 * @param existingEntries Existing schedule entries
 * @param excludeEntryId Optional entry ID to exclude from conflict checking
 * @returns Availability status and conflicting entries
 */
export const checkLecturerAvailability = (
  lecturer: Lecturer,
  timeRange: TimeRange,
  dayOfWeek: string,
  existingEntries: ScheduleEntry[],
  excludeEntryId?: string
): { status: AvailabilityStatus; conflicts: ScheduleEntry[] } => {
  const conflicts = existingEntries.filter(entry => {
    if (entry.id === excludeEntryId) return false;
    if (entry.day_of_week !== dayOfWeek) return false;
    if (entry.lecturer_id !== lecturer.id) return false;
    
    const entryRange = createTimeRange(entry.start_time, entry.end_time);
    return timeRangesOverlap(timeRange, entryRange);
  });
  
  // Check against lecturer preferences
  let status: AvailabilityStatus = conflicts.length > 0 ? 'conflict' : 'available';
  
  if (status === 'available' && lecturer.availability_preferences) {
    const prefs = lecturer.availability_preferences;
    
    // Check if day is preferred
    if (prefs.preferred_days && !prefs.preferred_days.includes(dayOfWeek)) {
      status = 'partially_available';
    }
    
    // Check if time is within preferred times
    if (prefs.preferred_times && status !== 'conflict') {
      const isInPreferredTime = prefs.preferred_times.some(prefTimeStr => {
        const prefRange = parseTimeRangeString(prefTimeStr);
        return prefRange && timeRangesOverlap(timeRange, prefRange);
      });
      
      if (!isInPreferredTime) {
        status = 'partially_available';
      }
    }
    
    // Check against blocked times
    if (prefs.blocked_times && status !== 'conflict') {
      const isInBlockedTime = prefs.blocked_times.some(blockedTimeStr => {
        const blockedRange = parseTimeRangeString(blockedTimeStr);
        return blockedRange && timeRangesOverlap(timeRange, blockedRange);
      });
      
      if (isInBlockedTime) {
        status = 'conflict';
      }
    }
  }
  
  return { status, conflicts };
};

/**
 * Checks availability of a class group for a given time range
 * @param classGroup Class group to check
 * @param timeRange Time range to check
 * @param dayOfWeek Day of the week
 * @param existingEntries Existing schedule entries
 * @param excludeEntryId Optional entry ID to exclude from conflict checking
 * @returns Availability status and conflicting entries
 */
export const checkClassGroupAvailability = (
  classGroup: ClassGroup,
  timeRange: TimeRange,
  dayOfWeek: string,
  existingEntries: ScheduleEntry[],
  excludeEntryId?: string
): { status: AvailabilityStatus; conflicts: ScheduleEntry[] } => {
  const conflicts = existingEntries.filter(entry => {
    if (entry.id === excludeEntryId) return false;
    if (entry.day_of_week !== dayOfWeek) return false;
    if (entry.group_id !== classGroup.id) return false;
    
    const entryRange = createTimeRange(entry.start_time, entry.end_time);
    return timeRangesOverlap(timeRange, entryRange);
  });
  
  const status: AvailabilityStatus = conflicts.length > 0 ? 'conflict' : 'available';
  return { status, conflicts };
};

/**
 * Comprehensive availability check for all resources
 * @param timeRange Time range to check
 * @param dayOfWeek Day of the week
 * @param classroomId Classroom ID
 * @param lecturerId Lecturer ID
 * @param groupId Class group ID
 * @param classrooms Array of all classrooms
 * @param lecturers Array of all lecturers
 * @param classGroups Array of all class groups
 * @param existingEntries Existing schedule entries
 * @param excludeEntryId Optional entry ID to exclude from conflict checking
 * @returns Overall availability status and detailed resource availability
 */
export const checkOverallAvailability = (
  timeRange: TimeRange,
  dayOfWeek: string,
  classroomId: string,
  lecturerId: string,
  groupId: string,
  classrooms: Classroom[],
  lecturers: Lecturer[],
  classGroups: ClassGroup[],
  existingEntries: ScheduleEntry[],
  excludeEntryId?: string
): {
  overall_status: AvailabilityStatus;
  classroom_availability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
  lecturer_availability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
  group_availability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
} => {
  const classroom = classrooms.find(c => c.id === classroomId);
  const lecturer = lecturers.find(l => l.id === lecturerId);
  const classGroup = classGroups.find(g => g.id === groupId);
  
  if (!classroom || !lecturer || !classGroup) {
    throw new Error('Required resources not found');
  }
  
  const classroomAvailability = checkClassroomAvailability(
    classroom, timeRange, dayOfWeek, existingEntries, excludeEntryId
  );
  
  const lecturerAvailability = checkLecturerAvailability(
    lecturer, timeRange, dayOfWeek, existingEntries, excludeEntryId
  );
  
  const groupAvailability = checkClassGroupAvailability(
    classGroup, timeRange, dayOfWeek, existingEntries, excludeEntryId
  );
  
  // Determine overall status
  let overallStatus: AvailabilityStatus = 'available';
  
  if (classroomAvailability.status === 'conflict' || 
      lecturerAvailability.status === 'conflict' || 
      groupAvailability.status === 'conflict') {
    overallStatus = 'conflict';
  } else if (classroomAvailability.status === 'partially_available' || 
             lecturerAvailability.status === 'partially_available' || 
             groupAvailability.status === 'partially_available') {
    overallStatus = 'partially_available';
  }
  
  return {
    overall_status: overallStatus,
    classroom_availability: classroomAvailability,
    lecturer_availability: lecturerAvailability,
    group_availability: groupAvailability
  };
};

/**
 * Generates availability slots for a resource for an entire day
 * @param resourceId Resource ID
 * @param resourceType Resource type
 * @param dayOfWeek Day of the week
 * @param existingEntries Existing schedule entries
 * @param timeIncrement Time increment in minutes (default: 15)
 * @returns Array of availability slots
 */
export const generateDayAvailability = (
  resourceId: string,
  resourceType: 'classroom' | 'lecturer' | 'group',
  dayOfWeek: string,
  existingEntries: ScheduleEntry[],
  timeIncrement: number = 15
): AvailabilitySlot[] => {
  const slots: AvailabilitySlot[] = [];
  const startHour = 8; // 8 AM
  const endHour = 18; // 6 PM
  
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += timeIncrement) {
      const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      const endMinute = minute + timeIncrement;
      const endHour = endMinute >= 60 ? hour + 1 : hour;
      const endTime = `${endHour.toString().padStart(2, '0')}:${(endMinute % 60).toString().padStart(2, '0')}`;
      
      if (endHour > 18) break; // Don't go past 6 PM
      
      const timeRange = createTimeRange(startTime, endTime);
      
      // Check for conflicts
      const conflicts = existingEntries.filter(entry => {
        if (entry.day_of_week !== dayOfWeek) return false;
        
        let hasResourceConflict = false;
        switch (resourceType) {
          case 'classroom':
            hasResourceConflict = entry.classroom_id === resourceId && !entry.is_online;
            break;
          case 'lecturer':
            hasResourceConflict = entry.lecturer_id === resourceId;
            break;
          case 'group':
            hasResourceConflict = entry.group_id === resourceId;
            break;
        }
        
        if (!hasResourceConflict) return false;
        
        const entryRange = createTimeRange(entry.start_time, entry.end_time);
        return timeRangesOverlap(timeRange, entryRange);
      });
      
      const status: AvailabilityStatus = conflicts.length > 0 ? 'busy' : 'available';
      
      slots.push({
        time_range: timeRange,
        status,
        conflicts: conflicts.length > 0 ? conflicts : undefined,
        resource_type: resourceType,
        resource_id: resourceId
      });
    }
  }
  
  return slots;
};
