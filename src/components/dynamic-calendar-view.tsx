"use client";

import React, { useState, useRef, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AvailabilityIndicator } from '@/components/availability-indicator';
import { ScheduleEntry, TimeRange, AvailabilityStatus } from '@/lib/types';
import { timeToMinutes, minutesToTime, createTimeRange } from '@/lib/time-utils';
import { cn } from '@/lib/utils';
import { Clock, GripVertical } from 'lucide-react';

interface DynamicCalendarViewProps {
  scheduleEntries: ScheduleEntry[];
  onEntryCreate?: (timeRange: TimeRange, day: string) => void;
  onEntryMove?: (entryId: string, newTimeRange: TimeRange, newDay: string) => void;
  onEntryResize?: (entryId: string, newTimeRange: TimeRange) => void;
  onEntryClick?: (entry: ScheduleEntry) => void;
  className?: string;
  timeIncrement?: number; // Minutes (default: 15)
  startHour?: number; // Default: 8 (8 AM)
  endHour?: number; // Default: 18 (6 PM)
  showAvailability?: boolean;
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

interface DragState {
  isDragging: boolean;
  dragType: 'create' | 'move' | 'resize';
  entryId?: string;
  startDay?: string;
  startTime?: number; // minutes
  currentDay?: string;
  currentTime?: number; // minutes
  originalEntry?: ScheduleEntry;
}

export default function DynamicCalendarView({
  scheduleEntries,
  onEntryCreate,
  onEntryMove,
  onEntryResize,
  onEntryClick,
  className,
  timeIncrement = 15,
  startHour = 8,
  endHour = 18,
  showAvailability = false
}: DynamicCalendarViewProps) {
  const [dragState, setDragState] = useState<DragState>({ isDragging: false, dragType: 'create' });
  const [hoveredSlot, setHoveredSlot] = useState<{ day: string; time: number } | null>(null);
  const calendarRef = useRef<HTMLDivElement>(null);

  // Generate time slots
  const timeSlots = useMemo(() => {
    const slots: number[] = [];
    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += timeIncrement) {
        slots.push(hour * 60 + minute);
      }
    }
    return slots;
  }, [startHour, endHour, timeIncrement]);

  // Calculate entry positions and dimensions
  const getEntryStyle = useCallback((entry: ScheduleEntry) => {
    const startMinutes = timeToMinutes(entry.start_time);
    const endMinutes = timeToMinutes(entry.end_time);
    const duration = endMinutes - startMinutes;
    
    const startSlotIndex = timeSlots.findIndex(slot => slot >= startMinutes);
    const slotHeight = 60; // Height per time slot in pixels
    
    return {
      top: startSlotIndex * slotHeight,
      height: (duration / timeIncrement) * slotHeight,
      zIndex: 10
    };
  }, [timeSlots, timeIncrement]);

  // Handle mouse events for drag operations
  const handleMouseDown = useCallback((e: React.MouseEvent, day: string, timeMinutes: number, entry?: ScheduleEntry) => {
    e.preventDefault();
    
    if (entry) {
      // Start moving existing entry
      setDragState({
        isDragging: true,
        dragType: 'move',
        entryId: entry.id,
        startDay: day,
        startTime: timeMinutes,
        currentDay: day,
        currentTime: timeMinutes,
        originalEntry: entry
      });
    } else {
      // Start creating new entry
      setDragState({
        isDragging: true,
        dragType: 'create',
        startDay: day,
        startTime: timeMinutes,
        currentDay: day,
        currentTime: timeMinutes
      });
    }
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!dragState.isDragging || !calendarRef.current) return;

    const rect = calendarRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Calculate which day and time slot
    const dayWidth = rect.width / (DAYS.length + 1); // +1 for time column
    const dayIndex = Math.floor((x - dayWidth) / dayWidth); // Subtract time column width
    const slotHeight = 60;
    const slotIndex = Math.floor(y / slotHeight);
    
    if (dayIndex >= 0 && dayIndex < DAYS.length && slotIndex >= 0 && slotIndex < timeSlots.length) {
      const day = DAYS[dayIndex];
      const timeMinutes = timeSlots[slotIndex];
      
      setDragState(prev => ({
        ...prev,
        currentDay: day,
        currentTime: timeMinutes
      }));
      
      setHoveredSlot({ day, time: timeMinutes });
    }
  }, [dragState.isDragging, timeSlots]);

  const handleMouseUp = useCallback(() => {
    if (!dragState.isDragging) return;

    const { dragType, startDay, startTime, currentDay, currentTime, entryId, originalEntry } = dragState;

    if (dragType === 'create' && startDay && startTime !== undefined && currentDay && currentTime !== undefined) {
      // Create new entry
      const startMinutes = Math.min(startTime, currentTime);
      const endMinutes = Math.max(startTime, currentTime) + timeIncrement; // Minimum duration
      
      const timeRange = createTimeRange(
        minutesToTime(startMinutes),
        minutesToTime(endMinutes)
      );
      
      onEntryCreate?.(timeRange, currentDay);
    } else if (dragType === 'move' && entryId && originalEntry && currentDay && currentTime !== undefined) {
      // Move existing entry
      const originalDuration = timeToMinutes(originalEntry.end_time) - timeToMinutes(originalEntry.start_time);
      const newStartTime = minutesToTime(currentTime);
      const newEndTime = minutesToTime(currentTime + originalDuration);
      
      const newTimeRange = createTimeRange(newStartTime, newEndTime);
      
      onEntryMove?.(entryId, newTimeRange, currentDay);
    }

    setDragState({ isDragging: false, dragType: 'create' });
    setHoveredSlot(null);
  }, [dragState, timeIncrement, onEntryCreate, onEntryMove]);

  // Get entries for a specific day
  const getEntriesForDay = useCallback((day: string) => {
    return scheduleEntries.filter(entry => entry.day_of_week === day);
  }, [scheduleEntries]);

  // Render drag preview
  const renderDragPreview = () => {
    if (!dragState.isDragging || !dragState.startDay || !dragState.currentDay || 
        dragState.startTime === undefined || dragState.currentTime === undefined) {
      return null;
    }

    const { dragType, startTime, currentTime } = dragState;
    
    if (dragType === 'create') {
      const startMinutes = Math.min(startTime, currentTime);
      const endMinutes = Math.max(startTime, currentTime) + timeIncrement;
      const duration = endMinutes - startMinutes;
      
      const startSlotIndex = timeSlots.findIndex(slot => slot >= startMinutes);
      const slotHeight = 60;
      
      return (
        <div
          className="absolute bg-primary/30 border-2 border-primary border-dashed rounded pointer-events-none"
          style={{
            top: startSlotIndex * slotHeight,
            height: (duration / timeIncrement) * slotHeight,
            left: 0,
            right: 0,
            zIndex: 20
          }}
        >
          <div className="p-2 text-xs font-medium">
            New Class
            <br />
            {minutesToTime(startMinutes)} - {minutesToTime(endMinutes)}
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Dynamic Schedule Calendar
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div 
          ref={calendarRef}
          className="grid grid-cols-6 border rounded-lg overflow-hidden"
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {/* Header Row */}
          <div className="p-3 text-center font-semibold border-b bg-muted">
            Time
          </div>
          {DAYS.map((day) => (
            <div key={day} className="p-3 text-center font-semibold border-b border-l bg-muted">
              {day}
            </div>
          ))}

          {/* Time Slots */}
          {timeSlots.map((timeMinutes, index) => (
            <React.Fragment key={timeMinutes}>
              {/* Time Label */}
              <div className="p-2 text-sm text-center border-b bg-muted/50 flex items-center justify-center">
                {minutesToTime(timeMinutes)}
              </div>
              
              {/* Day Columns */}
              {DAYS.map((day) => {
                const dayEntries = getEntriesForDay(day);
                const slotEntries = dayEntries.filter(entry => {
                  const entryStart = timeToMinutes(entry.start_time);
                  const entryEnd = timeToMinutes(entry.end_time);
                  return timeMinutes >= entryStart && timeMinutes < entryEnd;
                });

                const isHovered = hoveredSlot?.day === day && hoveredSlot?.time === timeMinutes;

                return (
                  <div
                    key={`${day}-${timeMinutes}`}
                    className={cn(
                      "relative border-b border-l min-h-[60px] cursor-pointer transition-colors",
                      "hover:bg-muted/50",
                      isHovered && "bg-primary/10",
                      index % 4 === 0 && "border-t-2" // Hour markers
                    )}
                    onMouseDown={(e) => handleMouseDown(e, day, timeMinutes)}
                  >
                    {/* Render entries that start in this slot */}
                    {dayEntries
                      .filter(entry => timeToMinutes(entry.start_time) === timeMinutes)
                      .map((entry) => {
                        const style = getEntryStyle(entry);
                        return (
                          <div
                            key={entry.id}
                            className={cn(
                              "absolute left-1 right-1 rounded p-2 text-xs cursor-move",
                              "bg-primary text-primary-foreground shadow-sm",
                              "hover:shadow-md transition-shadow",
                              entry.is_conflicted && "bg-destructive text-destructive-foreground"
                            )}
                            style={{
                              top: 0,
                              height: style.height,
                              zIndex: style.zIndex
                            }}
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              handleMouseDown(e, day, timeMinutes, entry);
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              onEntryClick?.(entry);
                            }}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <div className="font-medium truncate">
                                  Module {entry.module_id.slice(-3)}
                                </div>
                                <div className="text-xs opacity-90">
                                  {entry.start_time} - {entry.end_time}
                                </div>
                              </div>
                              <GripVertical className="h-3 w-3 opacity-50 flex-shrink-0" />
                            </div>
                            
                            {entry.is_conflicted && (
                              <Badge variant="destructive" className="mt-1 text-xs">
                                Conflict
                              </Badge>
                            )}
                          </div>
                        );
                      })}

                    {/* Drag preview for current day */}
                    {dragState.isDragging && dragState.currentDay === day && renderDragPreview()}
                  </div>
                );
              })}
            </React.Fragment>
          ))}
        </div>

        {/* Instructions */}
        <div className="mt-4 text-sm text-muted-foreground space-y-1">
          <p>• Click and drag on empty slots to create new class blocks</p>
          <p>• Drag existing class blocks to move them to different times or days</p>
          <p>• Click on class blocks to edit their details</p>
        </div>
      </CardContent>
    </Card>
  );
}
