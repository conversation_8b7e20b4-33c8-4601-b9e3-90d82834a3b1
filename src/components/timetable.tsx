"use client";

import { ScheduleEntry } from '@/lib/types';
import ScheduleEntryCard from './schedule-entry-card';
import { Card, CardContent } from './ui/card';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, closestCenter, useDroppable } from '@dnd-kit/core';
import { useState } from 'react';

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
const TIME_SLOTS = [
  '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'
];

interface TimetableProps {
  scheduleEntries: ScheduleEntry[];
  onSlotClick: (day: string, time: string) => void;
  onEntryMove?: (entryId: string, newDay: string, newTime: string) => void;
  onEntryClick?: (entry: ScheduleEntry) => void;
  isBulkMode?: boolean;
  selectedEntries?: Set<string>;
  onEntrySelect?: (entryId: string, selected: boolean) => void;
  showAvailabilityIndicators?: boolean;
}

// Droppable slot component
const DroppableSlot = ({
  day,
  time,
  entry,
  onSlotClick,
  onEntryClick,
  isBulkMode,
  selectedEntries,
  onEntrySelect,
  showAvailabilityIndicators
}: {
  day: string;
  time: string;
  entry: ScheduleEntry | null;
  onSlotClick: (day: string, time: string) => void;
  onEntryClick?: (entry: ScheduleEntry) => void;
  isBulkMode?: boolean;
  selectedEntries?: Set<string>;
  onEntrySelect?: (entryId: string, selected: boolean) => void;
  showAvailabilityIndicators?: boolean;
}) => {
  const { isOver, setNodeRef } = useDroppable({
    id: `${day}-${time}`,
  });

  return (
    <div
      ref={setNodeRef}
      className={`p-1 border-b border-r min-h-[120px] transition-colors duration-200 relative ${
        isOver
          ? 'bg-accent/70 border-primary border-2'
          : entry
            ? 'bg-background/50'
            : 'bg-background/50 hover:bg-accent/50 cursor-pointer'
      }`}
      onClick={() => !entry && onSlotClick(day, time)}
    >
      {entry ? (
        <ScheduleEntryCard
          entry={entry}
          onClick={() => {
            if (isBulkMode && onEntrySelect) {
              const isSelected = selectedEntries.has(entry.id);
              onEntrySelect(entry.id, !isSelected);
            } else {
              onEntryClick?.(entry);
            }
          }}
          isBulkMode={isBulkMode}
          isSelected={selectedEntries.has(entry.id)}
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
          {isOver ? (
            <span>Drop here</span>
          ) : (
            <>
              Click to add
              {showAvailabilityIndicators && (
                <div className="absolute top-1 right-1 w-2 h-2 bg-green-400 rounded-full" title="Available slot" />
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

const Timetable = ({
  scheduleEntries,
  onSlotClick,
  onEntryMove,
  onEntryClick,
  isBulkMode = false,
  selectedEntries = new Set(),
  onEntrySelect,
  showAvailabilityIndicators = false
}: TimetableProps) => {
  const [activeEntry, setActiveEntry] = useState<ScheduleEntry | null>(null);

  const getEntryForSlot = (day: string, time: string) => {
    return scheduleEntries.find(
      (entry) => entry.day_of_week === day && entry.start_time === time
    );
  };

  const handleDragStart = (event: DragStartEvent) => {
    const entryId = event.active.id as string;
    const entry = scheduleEntries.find(e => e.id === entryId);
    setActiveEntry(entry || null);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveEntry(null);

    if (!over || !onEntryMove) return;

    const entryId = active.id as string;
    const dropZoneId = over.id as string;

    // Parse the drop zone ID to get day and time
    const [day, time] = dropZoneId.split('-');

    if (day && time && DAYS.includes(day) && TIME_SLOTS.includes(time)) {
      // Check if the slot is empty or if we're dropping on the same slot
      const existingEntry = getEntryForSlot(day, time);
      const currentEntry = scheduleEntries.find(e => e.id === entryId);

      if (!existingEntry || existingEntry.id === entryId) {
        // Only move if it's actually a different slot
        if (currentEntry && (currentEntry.day_of_week !== day || currentEntry.start_time !== time)) {
          onEntryMove(entryId, day, time);
        }
      }
    }
  };

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <Card>
        <CardContent className="p-0">
          {/* Mobile View - Vertical Layout */}
          <div className="block md:hidden">
            <div className="space-y-4 p-4">
              {DAYS.map((day) => (
                <div key={day} className="border rounded-lg overflow-hidden">
                  <div className="bg-card p-3 border-b">
                    <h3 className="font-semibold text-center">{day}</h3>
                  </div>
                  <div className="space-y-2 p-2">
                    {TIME_SLOTS.slice(0, -1).map((time, index) => {
                      const entry = getEntryForSlot(day, time);
                      return (
                        <div key={time} className="border rounded p-2 min-h-[80px] bg-background">
                          <div className="text-xs text-muted-foreground mb-1">
                            {time} - {TIME_SLOTS[index + 1]}
                          </div>
                          <DroppableSlot
                            day={day}
                            time={time}
                            entry={entry}
                            onSlotClick={onSlotClick}
                            onEntryClick={onEntryClick}
                            isBulkMode={isBulkMode}
                            selectedEntries={selectedEntries}
                            onEntrySelect={onEntrySelect}
                            showAvailabilityIndicators={showAvailabilityIndicators}
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Desktop View - Grid Layout */}
          <div className="hidden md:block">
            <div className="grid grid-cols-6 border-t border-l rounded-lg overflow-hidden">
            {/* Header Row */}
            <div className="p-2 border-b border-r bg-card"></div>
            {DAYS.map((day) => (
              <div
                key={day}
                className="p-3 text-center font-semibold border-b border-r bg-card"
              >
                {day}
              </div>
            ))}

            {/* Time Slots Rows */}
            {TIME_SLOTS.slice(0, -1).map((time, index) => (
              <div key={time} className="grid grid-cols-6 contents">
                <div className="p-3 text-center font-semibold border-b border-r bg-card flex items-center justify-center">
                  <span>
                    {time} - {TIME_SLOTS[index + 1]}
                  </span>
                </div>
                {DAYS.map((day) => {
                  const entry = getEntryForSlot(day, time);
                  return (
                    <DroppableSlot
                      key={`${day}-${time}`}
                      day={day}
                      time={time}
                      entry={entry}
                      onSlotClick={onSlotClick}
                      onEntryClick={onEntryClick}
                      isBulkMode={isBulkMode}
                      selectedEntries={selectedEntries}
                      onEntrySelect={onEntrySelect}
                      showAvailabilityIndicators={showAvailabilityIndicators}
                    />
                  );
                })}
              </div>
            ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Drag Overlay */}
      <DragOverlay>
        {activeEntry ? (
          <ScheduleEntryCard entry={activeEntry} isDragging />
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};

export default Timetable;
