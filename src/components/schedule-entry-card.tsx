"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Lecturer } from '@/lib/types';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AlertTriangle, Video, GripVertical, Check } from 'lucide-react';
import { useEffect, useState } from 'react';
import { getModules, getLecturers } from '@/lib/api-firestore';
import { useDraggable } from '@dnd-kit/core';

interface ScheduleEntryCardProps {
  entry: ScheduleEntry;
  isDragging?: boolean;
  onClick?: () => void;
  isBulkMode?: boolean;
  isSelected?: boolean;
}

const ScheduleEntryCard = ({
  entry,
  isDragging = false,
  onClick,
  isBulkMode = false,
  isSelected = false
}: ScheduleEntryCardProps) => {
  const [module, setModule] = useState<Module | null>(null);
  const [lecturer, setLecturer] = useState<Lecturer | null>(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: isDraggingFromKit,
  } = useDraggable({
    id: entry.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  useEffect(() => {
    const fetchDetails = async () => {
      const modules = await getModules();
      const lecturers = await getLecturers();
      setModule(modules.find(m => m.id === entry.module_id) || null);
      setLecturer(lecturers.find(l => l.id === entry.lecturer_id) || null);
    };
    fetchDetails();
  }, [entry.module_id, entry.lecturer_id]);

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={cn(
        'h-full w-full shadow-md hover:shadow-lg transition-shadow duration-200 relative cursor-grab active:cursor-grabbing',
        entry.is_conflicted && 'border-destructive border-2',
        (isDragging || isDraggingFromKit) && 'opacity-50 z-50',
        isBulkMode && 'cursor-pointer',
        isSelected && 'ring-2 ring-primary bg-primary/10'
      )}
      onClick={onClick}
      {...attributes}
      {...listeners}
    >
      <CardContent className="p-2 text-xs">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <p className="font-bold truncate">{module?.module_name || 'Loading...'}</p>
            <p className="text-muted-foreground truncate">{lecturer?.name || '...'}</p>
            <p className="text-muted-foreground">CR: {entry.classroom_id}</p>
            <p className="text-muted-foreground">Group: {entry.group_id}</p>
          </div>
          <div className="flex items-center gap-1">
            {isBulkMode && isSelected && (
              <Check className="h-3 w-3 text-primary flex-shrink-0" />
            )}
            {!isBulkMode && (
              <GripVertical className="h-3 w-3 text-muted-foreground flex-shrink-0" />
            )}
          </div>
        </div>
        <div className="absolute bottom-1 right-1 flex gap-1">
          {entry.is_online && (
            <Badge variant="secondary" className="p-1">
              <Video className="h-3 w-3" />
            </Badge>
          )}
          {entry.is_conflicted && (
            <Badge variant="destructive" className="p-1">
              <AlertTriangle className="h-3 w-3" />
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ScheduleEntryCard;
