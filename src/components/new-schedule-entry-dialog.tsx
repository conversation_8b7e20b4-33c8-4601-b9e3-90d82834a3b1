"use client";

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Classroom, Lecturer, ClassGroup, Module, ScheduleEntry } from '@/lib/types';
import { useEffect } from 'react';

const FormSchema = z.object({
  module_id: z.string().min(1, "Module is required."),
  lecturer_id: z.string().min(1, "Lecturer is required."),
  classroom_id: z.string().min(1, "Classroom is required."),
  group_id: z.string().min(1, "Class group is required."),
  day_of_week: z.string().min(1, "Day is required."),
  start_time: z.string().min(1, "Start time is required."),
  is_online: z.boolean(),
});

type FormValues = z.infer<typeof FormSchema>;

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
const TIME_SLOTS = ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'];

interface NewScheduleEntryDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onAddEntry: (entry: Omit<ScheduleEntry, 'id'>) => void;
  classrooms: Classroom[];
  lecturers: Lecturer[];
  classGroups: ClassGroup[];
  modules: Module[];
  initialSlot?: { day: string; time: string } | null;
}

export default function NewScheduleEntryDialog({
  isOpen,
  onOpenChange,
  onAddEntry,
  classrooms,
  lecturers,
  classGroups,
  modules,
  initialSlot,
}: NewScheduleEntryDialogProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      is_online: false,
      day_of_week: initialSlot?.day || '',
      start_time: initialSlot?.time || '',
    },
  });
  
  useEffect(() => {
    if (initialSlot) {
      form.reset({
        ...form.getValues(),
        day_of_week: initialSlot.day,
        start_time: initialSlot.time,
      });
    }
  }, [initialSlot, form]);

  const onSubmit = (data: FormValues) => {
    const startTimeIndex = TIME_SLOTS.indexOf(data.start_time);
    const end_time = startTimeIndex !== -1 && startTimeIndex < TIME_SLOTS.length - 1 
      ? TIME_SLOTS[startTimeIndex + 1] 
      : '18:00';

    onAddEntry({ ...data, end_time, day_of_week: data.day_of_week as ScheduleEntry['day_of_week'] });
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Schedule Entry</DialogTitle>
          <DialogDescription>Fill in the details for the new class session.</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
            <FormField
              control={form.control}
              name="module_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Module</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger><SelectValue placeholder="Select a module" /></SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {modules.map(m => <SelectItem key={m.id} value={m.id}>{m.module_name}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lecturer_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Lecturer</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger><SelectValue placeholder="Select a lecturer" /></SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {lecturers.map(l => <SelectItem key={l.id} value={l.id}>{l.name}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="classroom_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Classroom</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger><SelectValue placeholder="Select a classroom" /></SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {classrooms.map(c => <SelectItem key={c.id} value={c.id}>{c.room_number}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="group_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Class Group</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger><SelectValue placeholder="Select a group" /></SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {classGroups.map(g => <SelectItem key={g.id} value={g.id}>{g.program_name} - Year {g.year_level}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="day_of_week"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Day</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="Select day" /></SelectTrigger></FormControl>
                      <SelectContent>
                        {DAYS.map(d => <SelectItem key={d} value={d}>{d}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
               <FormField
                control={form.control}
                name="start_time"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Time</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="Select time" /></SelectTrigger></FormControl>
                      <SelectContent>
                        {TIME_SLOTS.slice(0, -1).map(t => <SelectItem key={t} value={t}>{t}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="is_online"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Online Class</FormLabel>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit">Add Entry</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
