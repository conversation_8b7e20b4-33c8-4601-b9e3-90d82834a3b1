"use client";

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { TimeRangePicker, DurationDisplay } from '@/components/ui/time-picker';
import { OverallAvailabilityDisplay } from '@/components/availability-indicator';
import { Classroom, Lecturer, ClassGroup, Module, ScheduleEntry, TimeRange, AvailabilityStatus } from '@/lib/types';
import { useEffect, useState, useMemo } from 'react';
import { createTimeRange, isValidTimeFormat } from '@/lib/time-utils';
import { checkOverallAvailability } from '@/lib/availability-checker';
import { Loader2 } from 'lucide-react';

const FormSchema = z.object({
  module_id: z.string().min(1, "Module is required."),
  lecturer_id: z.string().min(1, "Lecturer is required."),
  classroom_id: z.string().min(1, "Classroom is required."),
  group_id: z.string().min(1, "Class group is required."),
  day_of_week: z.string().min(1, "Day is required."),
  start_time: z.string().min(1, "Start time is required."),
  end_time: z.string().min(1, "End time is required."),
  is_online: z.boolean(),
});

type FormValues = z.infer<typeof FormSchema>;

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

interface DynamicScheduleEntryDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onAddEntry?: (entry: Omit<ScheduleEntry, 'id'>) => void;
  onUpdateEntry?: (entryId: string, entry: Partial<ScheduleEntry>) => void;
  onDeleteEntry?: (entryId: string) => void;
  classrooms: Classroom[];
  lecturers: Lecturer[];
  classGroups: ClassGroup[];
  modules: Module[];
  existingEntries: ScheduleEntry[];
  initialSlot?: { day: string; time: string } | null;
  editingEntry?: ScheduleEntry | null; // For editing mode
  mode?: 'create' | 'edit';
}

export default function DynamicScheduleEntryDialog({
  isOpen,
  onOpenChange,
  onAddEntry,
  onUpdateEntry,
  onDeleteEntry,
  classrooms,
  lecturers,
  classGroups,
  modules,
  existingEntries,
  initialSlot,
  editingEntry,
  mode = 'create',
}: DynamicScheduleEntryDialogProps) {
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false);
  const [availabilityResult, setAvailabilityResult] = useState<{
    overall_status: AvailabilityStatus;
    classroom_availability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
    lecturer_availability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
    group_availability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
  } | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      is_online: false,
      day_of_week: initialSlot?.day || editingEntry?.day_of_week || '',
      start_time: initialSlot?.time || editingEntry?.start_time || '',
      end_time: editingEntry?.end_time || '',
      module_id: editingEntry?.module_id || '',
      lecturer_id: editingEntry?.lecturer_id || '',
      classroom_id: editingEntry?.classroom_id || '',
      group_id: editingEntry?.group_id || '',
    },
  });

  const watchedValues = form.watch();

  useEffect(() => {
    if (mode === 'edit' && editingEntry) {
      form.reset({
        module_id: editingEntry.module_id,
        lecturer_id: editingEntry.lecturer_id,
        classroom_id: editingEntry.classroom_id,
        group_id: editingEntry.group_id,
        day_of_week: editingEntry.day_of_week,
        start_time: editingEntry.start_time,
        end_time: editingEntry.end_time,
        is_online: editingEntry.is_online,
      });
    } else if (initialSlot) {
      form.reset({
        ...form.getValues(),
        day_of_week: initialSlot.day,
        start_time: initialSlot.time,
        end_time: '',
      });
    }
  }, [initialSlot, editingEntry, mode, form]);

  // Real-time availability checking
  useEffect(() => {
    const checkAvailability = async () => {
      const { start_time, end_time, day_of_week, classroom_id, lecturer_id, group_id } = watchedValues;
      
      if (!start_time || !end_time || !day_of_week || !classroom_id || !lecturer_id || !group_id) {
        setAvailabilityResult(null);
        return;
      }

      if (!isValidTimeFormat(start_time) || !isValidTimeFormat(end_time)) {
        setAvailabilityResult(null);
        return;
      }

      setIsCheckingAvailability(true);
      
      try {
        const timeRange = createTimeRange(start_time, end_time);
        if (timeRange.duration_minutes <= 0) {
          setAvailabilityResult(null);
          return;
        }

        const result = checkOverallAvailability(
          timeRange,
          day_of_week,
          classroom_id,
          lecturer_id,
          group_id,
          classrooms,
          lecturers,
          classGroups,
          existingEntries
        );
        
        setAvailabilityResult(result);
      } catch (error) {
        console.error('Error checking availability:', error);
        setAvailabilityResult(null);
      } finally {
        setIsCheckingAvailability(false);
      }
    };

    const debounceTimer = setTimeout(checkAvailability, 300);
    return () => clearTimeout(debounceTimer);
  }, [watchedValues, classrooms, lecturers, classGroups, existingEntries]);

  const selectedClassroom = useMemo(() => 
    classrooms.find(c => c.id === watchedValues.classroom_id),
    [classrooms, watchedValues.classroom_id]
  );

  const selectedLecturer = useMemo(() => 
    lecturers.find(l => l.id === watchedValues.lecturer_id),
    [lecturers, watchedValues.lecturer_id]
  );

  const selectedGroup = useMemo(() => 
    classGroups.find(g => g.id === watchedValues.group_id),
    [classGroups, watchedValues.group_id]
  );

  const currentTimeRange = useMemo(() => {
    if (!watchedValues.start_time || !watchedValues.end_time) return null;
    if (!isValidTimeFormat(watchedValues.start_time) || !isValidTimeFormat(watchedValues.end_time)) return null;
    
    try {
      return createTimeRange(watchedValues.start_time, watchedValues.end_time);
    } catch {
      return null;
    }
  }, [watchedValues.start_time, watchedValues.end_time]);

  const onSubmit = (data: FormValues) => {
    if (availabilityResult?.overall_status === 'conflict') {
      // Could show a confirmation dialog here
      if (!confirm('This time slot has conflicts. Do you want to proceed anyway?')) {
        return;
      }
    }

    const duration_minutes = currentTimeRange?.duration_minutes;

    if (mode === 'edit' && editingEntry && onUpdateEntry) {
      onUpdateEntry(editingEntry.id, {
        ...data,
        day_of_week: data.day_of_week as ScheduleEntry['day_of_week'],
        duration_minutes
      });
    } else if (mode === 'create' && onAddEntry) {
      onAddEntry({
        ...data,
        day_of_week: data.day_of_week as ScheduleEntry['day_of_week'],
        duration_minutes
      });
    }

    form.reset();
    onOpenChange(false);
  };

  const handleDelete = () => {
    if (mode === 'edit' && editingEntry && onDeleteEntry) {
      if (confirm('Are you sure you want to delete this schedule entry?')) {
        onDeleteEntry(editingEntry.id);
        onOpenChange(false);
      }
    }
  };

  const handleStartTimeChange = (time: string) => {
    form.setValue('start_time', time);
    
    // Auto-set end time if not set or invalid
    if (!form.getValues('end_time') || form.getValues('end_time') <= time) {
      const [hours, minutes] = time.split(':').map(Number);
      const startMinutes = hours * 60 + minutes;
      const endMinutes = startMinutes + 60; // Default 1 hour duration
      const endHours = Math.floor(endMinutes / 60);
      const endMins = endMinutes % 60;
      const endTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
      form.setValue('end_time', endTime);
    }
  };

  const handleEndTimeChange = (time: string) => {
    form.setValue('end_time', time);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'edit' ? 'Edit Schedule Entry' : 'Add New Schedule Entry'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'edit'
              ? 'Update the details for this class session. Real-time availability checking is enabled.'
              : 'Fill in the details for the new class session. Real-time availability checking is enabled.'
            }
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
            <FormField
              control={form.control}
              name="module_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Module</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger><SelectValue placeholder="Select a module" /></SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {modules.map(m => <SelectItem key={m.id} value={m.id}>{m.module_name}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="lecturer_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lecturer</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger><SelectValue placeholder="Select a lecturer" /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {lecturers.map(l => <SelectItem key={l.id} value={l.id}>{l.name}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="classroom_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Classroom</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger><SelectValue placeholder="Select a classroom" /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {classrooms.map(c => <SelectItem key={c.id} value={c.id}>{c.room_number}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="group_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Class Group</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger><SelectValue placeholder="Select a group" /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {classGroups.map(g => <SelectItem key={g.id} value={g.id}>{g.program_name} - Year {g.year_level}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="day_of_week"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Day</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="Select day" /></SelectTrigger></FormControl>
                      <SelectContent>
                        {DAYS.map(d => <SelectItem key={d} value={d}>{d}</SelectItem>)}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <FormLabel>Time Range</FormLabel>
              <TimeRangePicker
                startTime={watchedValues.start_time}
                endTime={watchedValues.end_time}
                onStartTimeChange={handleStartTimeChange}
                onEndTimeChange={handleEndTimeChange}
                increment={15}
                minTime="08:00"
                maxTime="18:00"
                minDuration={30}
                maxDuration={240}
              />
              
              <DurationDisplay 
                startTime={watchedValues.start_time} 
                endTime={watchedValues.end_time}
                className="text-center"
              />
            </div>

            <FormField
              control={form.control}
              name="is_online"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Online Class</FormLabel>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Real-time Availability Display */}
            {isCheckingAvailability && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Checking availability...</span>
              </div>
            )}

            {availabilityResult && currentTimeRange && (
              <OverallAvailabilityDisplay
                overallStatus={availabilityResult.overall_status}
                classroomAvailability={availabilityResult.classroom_availability}
                lecturerAvailability={availabilityResult.lecturer_availability}
                groupAvailability={availabilityResult.group_availability}
                classroomName={selectedClassroom?.room_number}
                lecturerName={selectedLecturer?.name}
                groupName={selectedGroup ? `${selectedGroup.program_name} - Year ${selectedGroup.year_level}` : undefined}
                timeRange={currentTimeRange}
              />
            )}

            <DialogFooter className="flex justify-between">
              {mode === 'edit' && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={handleDelete}
                >
                  Delete Entry
                </Button>
              )}
              <div className="flex gap-2 ml-auto">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={availabilityResult?.overall_status === 'conflict' && !confirm}
                >
                  {mode === 'edit' ? 'Update Entry' : 'Add Entry'}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
