"use client";

import * as React from "react";
import { Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { isValidTimeFormat } from "@/lib/time-utils";

interface TimePickerProps {
  value?: string;
  onChange: (time: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  increment?: number; // Minutes increment (default: 15)
  minTime?: string; // Earliest selectable time
  maxTime?: string; // Latest selectable time
}

export function TimePicker({
  value,
  onChange,
  placeholder = "Select time",
  disabled = false,
  className,
  increment = 15,
  minTime = "08:00",
  maxTime = "18:00"
}: TimePickerProps) {
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState(value || "");

  React.useEffect(() => {
    setInputValue(value || "");
  }, [value]);

  const generateTimeOptions = React.useMemo(() => {
    const options: string[] = [];
    const [minHour, minMinute] = minTime.split(':').map(Number);
    const [maxHour, maxMinute] = maxTime.split(':').map(Number);
    
    const startMinutes = minHour * 60 + minMinute;
    const endMinutes = maxHour * 60 + maxMinute;
    
    for (let minutes = startMinutes; minutes <= endMinutes; minutes += increment) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      const timeStr = `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
      options.push(timeStr);
    }
    
    return options;
  }, [minTime, maxTime, increment]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // Validate and update if valid
    if (isValidTimeFormat(newValue)) {
      onChange(newValue);
    }
  };

  const handleInputBlur = () => {
    // If input is invalid, revert to last valid value
    if (!isValidTimeFormat(inputValue)) {
      setInputValue(value || "");
    }
  };

  const handleTimeSelect = (time: string) => {
    setInputValue(time);
    onChange(time);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !value && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <Clock className="mr-2 h-4 w-4" />
          <Input
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            placeholder={placeholder}
            className="border-0 p-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0"
            disabled={disabled}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <ScrollArea className="h-60">
          <div className="p-2">
            {generateTimeOptions.map((time) => (
              <Button
                key={time}
                variant={value === time ? "default" : "ghost"}
                className="w-full justify-start font-mono"
                onClick={() => handleTimeSelect(time)}
              >
                {time}
              </Button>
            ))}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}

interface TimeRangePickerProps {
  startTime?: string;
  endTime?: string;
  onStartTimeChange: (time: string) => void;
  onEndTimeChange: (time: string) => void;
  disabled?: boolean;
  className?: string;
  increment?: number;
  minTime?: string;
  maxTime?: string;
  minDuration?: number; // Minimum duration in minutes
  maxDuration?: number; // Maximum duration in minutes
}

export function TimeRangePicker({
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
  disabled = false,
  className,
  increment = 15,
  minTime = "08:00",
  maxTime = "18:00",
  minDuration = 30,
  maxDuration = 480 // 8 hours
}: TimeRangePickerProps) {
  const handleStartTimeChange = (time: string) => {
    onStartTimeChange(time);
    
    // Auto-adjust end time if needed
    if (endTime && time >= endTime) {
      const [hours, minutes] = time.split(':').map(Number);
      const startMinutes = hours * 60 + minutes;
      const newEndMinutes = Math.min(startMinutes + minDuration, 18 * 60); // Don't go past 6 PM
      const newEndHours = Math.floor(newEndMinutes / 60);
      const newEndMins = newEndMinutes % 60;
      const newEndTime = `${newEndHours.toString().padStart(2, '0')}:${newEndMins.toString().padStart(2, '0')}`;
      onEndTimeChange(newEndTime);
    }
  };

  const handleEndTimeChange = (time: string) => {
    onEndTimeChange(time);
  };

  // Calculate dynamic max time for end time picker based on start time
  const dynamicMaxTime = React.useMemo(() => {
    if (!startTime) return maxTime;
    
    const [hours, minutes] = startTime.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    const maxEndMinutes = Math.min(startMinutes + maxDuration, 18 * 60);
    const maxEndHours = Math.floor(maxEndMinutes / 60);
    const maxEndMins = maxEndMinutes % 60;
    
    return `${maxEndHours.toString().padStart(2, '0')}:${maxEndMins.toString().padStart(2, '0')}`;
  }, [startTime, maxDuration]);

  // Calculate dynamic min time for end time picker based on start time
  const dynamicMinTime = React.useMemo(() => {
    if (!startTime) return minTime;
    
    const [hours, minutes] = startTime.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    const minEndMinutes = startMinutes + minDuration;
    const minEndHours = Math.floor(minEndMinutes / 60);
    const minEndMins = minEndMinutes % 60;
    
    return `${minEndHours.toString().padStart(2, '0')}:${minEndMins.toString().padStart(2, '0')}`;
  }, [startTime, minDuration]);

  return (
    <div className={cn("grid grid-cols-2 gap-4", className)}>
      <div className="space-y-2">
        <Label>Start Time</Label>
        <TimePicker
          value={startTime}
          onChange={handleStartTimeChange}
          placeholder="Start time"
          disabled={disabled}
          increment={increment}
          minTime={minTime}
          maxTime={maxTime}
        />
      </div>
      <div className="space-y-2">
        <Label>End Time</Label>
        <TimePicker
          value={endTime}
          onChange={handleEndTimeChange}
          placeholder="End time"
          disabled={disabled}
          increment={increment}
          minTime={dynamicMinTime}
          maxTime={dynamicMaxTime}
        />
      </div>
    </div>
  );
}

interface DurationDisplayProps {
  startTime?: string;
  endTime?: string;
  className?: string;
}

export function DurationDisplay({ startTime, endTime, className }: DurationDisplayProps) {
  const duration = React.useMemo(() => {
    if (!startTime || !endTime) return null;
    
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    const [endHours, endMinutes] = endTime.split(':').map(Number);
    
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;
    
    const durationMinutes = endTotalMinutes - startTotalMinutes;
    
    if (durationMinutes <= 0) return null;
    
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    
    if (hours === 0) return `${minutes}m`;
    if (minutes === 0) return `${hours}h`;
    return `${hours}h ${minutes}m`;
  }, [startTime, endTime]);

  if (!duration) return null;

  return (
    <div className={cn("text-sm text-muted-foreground", className)}>
      Duration: {duration}
    </div>
  );
}
