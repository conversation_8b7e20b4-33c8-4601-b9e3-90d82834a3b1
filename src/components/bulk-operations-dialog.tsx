"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Copy, RotateCcw, Calendar, Layers } from 'lucide-react';
import { ScheduleEntry, Classroom, Lecturer, ClassGroup, Module } from '@/lib/types';

interface BulkOperationsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  scheduleEntries: ScheduleEntry[];
  classrooms: Classroom[];
  lecturers: Lecturer[];
  classGroups: ClassGroup[];
  modules: Module[];
  onBulkCreate: (entries: Omit<ScheduleEntry, 'id'>[]) => void;
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

export default function BulkOperationsDialog({
  isOpen,
  onOpenChange,
  scheduleEntries,
  classrooms,
  lecturers,
  classGroups,
  modules,
  onBulkCreate
}: BulkOperationsDialogProps) {
  const [operationType, setOperationType] = useState<'copy' | 'recurring' | 'template'>('copy');
  const [selectedEntry, setSelectedEntry] = useState<string>('');
  const [targetDays, setTargetDays] = useState<string[]>([]);
  const [recurringWeeks, setRecurringWeeks] = useState<number>(4);
  const [templateData, setTemplateData] = useState({
    module_id: '',
    lecturer_id: '',
    classroom_id: '',
    group_id: '',
    is_online: false
  });
  const [selectedTimeSlots, setSelectedTimeSlots] = useState<string[]>([]);

  const handleDayToggle = (day: string) => {
    setTargetDays(prev => 
      prev.includes(day) 
        ? prev.filter(d => d !== day)
        : [...prev, day]
    );
  };

  const handleTimeSlotToggle = (timeSlot: string) => {
    setSelectedTimeSlots(prev => 
      prev.includes(timeSlot) 
        ? prev.filter(t => t !== timeSlot)
        : [...prev, timeSlot]
    );
  };

  const handleCopySchedule = () => {
    const sourceEntry = scheduleEntries.find(e => e.id === selectedEntry);
    if (!sourceEntry || targetDays.length === 0) return;

    const newEntries: Omit<ScheduleEntry, 'id'>[] = targetDays.map(day => ({
      module_id: sourceEntry.module_id,
      lecturer_id: sourceEntry.lecturer_id,
      classroom_id: sourceEntry.classroom_id,
      group_id: sourceEntry.group_id,
      day_of_week: day as ScheduleEntry['day_of_week'],
      start_time: sourceEntry.start_time,
      end_time: sourceEntry.end_time,
      is_online: sourceEntry.is_online,
      is_conflicted: false
    }));

    onBulkCreate(newEntries);
    onOpenChange(false);
  };

  const handleCreateRecurring = () => {
    const sourceEntry = scheduleEntries.find(e => e.id === selectedEntry);
    if (!sourceEntry || recurringWeeks <= 0) return;

    // For simplicity, we'll create the same entry for multiple weeks
    // In a real app, you'd handle date calculations properly
    const newEntries: Omit<ScheduleEntry, 'id'>[] = [];
    
    for (let week = 1; week <= recurringWeeks; week++) {
      newEntries.push({
        module_id: sourceEntry.module_id,
        lecturer_id: sourceEntry.lecturer_id,
        classroom_id: sourceEntry.classroom_id,
        group_id: sourceEntry.group_id,
        day_of_week: sourceEntry.day_of_week,
        start_time: sourceEntry.start_time,
        end_time: sourceEntry.end_time,
        is_online: sourceEntry.is_online,
        is_conflicted: false
      });
    }

    onBulkCreate(newEntries);
    onOpenChange(false);
  };

  const handleCreateFromTemplate = () => {
    if (!templateData.module_id || !templateData.lecturer_id || 
        !templateData.classroom_id || !templateData.group_id ||
        targetDays.length === 0 || selectedTimeSlots.length === 0) return;

    const newEntries: Omit<ScheduleEntry, 'id'>[] = [];
    
    targetDays.forEach(day => {
      selectedTimeSlots.forEach(timeSlot => {
        // Calculate end time (assuming 1-hour slots)
        const [hour, minute] = timeSlot.split(':').map(Number);
        const endHour = hour + 1;
        const end_time = `${endHour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        
        newEntries.push({
          module_id: templateData.module_id,
          lecturer_id: templateData.lecturer_id,
          classroom_id: templateData.classroom_id,
          group_id: templateData.group_id,
          day_of_week: day as ScheduleEntry['day_of_week'],
          start_time: timeSlot,
          end_time,
          is_online: templateData.is_online,
          is_conflicted: false
        });
      });
    });

    onBulkCreate(newEntries);
    onOpenChange(false);
  };

  const getAvailableTimeSlots = () => {
    return ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Scheduling Operations</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Operation Type Selection */}
          <div className="grid grid-cols-3 gap-4">
            <Card 
              className={`cursor-pointer transition-colors ${operationType === 'copy' ? 'ring-2 ring-primary' : ''}`}
              onClick={() => setOperationType('copy')}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Copy className="h-4 w-4" />
                  Copy Schedule
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-muted-foreground">Copy an existing entry to multiple days</p>
              </CardContent>
            </Card>
            
            <Card 
              className={`cursor-pointer transition-colors ${operationType === 'recurring' ? 'ring-2 ring-primary' : ''}`}
              onClick={() => setOperationType('recurring')}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <RotateCcw className="h-4 w-4" />
                  Recurring Entry
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-muted-foreground">Create recurring entries for multiple weeks</p>
              </CardContent>
            </Card>
            
            <Card 
              className={`cursor-pointer transition-colors ${operationType === 'template' ? 'ring-2 ring-primary' : ''}`}
              onClick={() => setOperationType('template')}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Layers className="h-4 w-4" />
                  Template
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-xs text-muted-foreground">Create multiple entries from template</p>
              </CardContent>
            </Card>
          </div>

          {/* Copy Schedule */}
          {operationType === 'copy' && (
            <div className="space-y-4">
              <div>
                <Label>Select Entry to Copy</Label>
                <Select value={selectedEntry} onValueChange={setSelectedEntry}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an existing entry" />
                  </SelectTrigger>
                  <SelectContent>
                    {scheduleEntries.map(entry => {
                      const module = modules.find(m => m.id === entry.module_id);
                      const lecturer = lecturers.find(l => l.id === entry.lecturer_id);
                      return (
                        <SelectItem key={entry.id} value={entry.id}>
                          {module?.module_name} - {lecturer?.name} ({entry.day_of_week} {entry.start_time})
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>Target Days</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {DAYS.map(day => (
                    <div key={day} className="flex items-center space-x-2">
                      <Checkbox
                        id={`copy-${day}`}
                        checked={targetDays.includes(day)}
                        onCheckedChange={() => handleDayToggle(day)}
                      />
                      <Label htmlFor={`copy-${day}`} className="text-sm">{day}</Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <Button onClick={handleCopySchedule} disabled={!selectedEntry || targetDays.length === 0}>
                Copy to Selected Days
              </Button>
            </div>
          )}

          {/* Recurring Entry */}
          {operationType === 'recurring' && (
            <div className="space-y-4">
              <div>
                <Label>Select Entry to Repeat</Label>
                <Select value={selectedEntry} onValueChange={setSelectedEntry}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an existing entry" />
                  </SelectTrigger>
                  <SelectContent>
                    {scheduleEntries.map(entry => {
                      const module = modules.find(m => m.id === entry.module_id);
                      const lecturer = lecturers.find(l => l.id === entry.lecturer_id);
                      return (
                        <SelectItem key={entry.id} value={entry.id}>
                          {module?.module_name} - {lecturer?.name} ({entry.day_of_week} {entry.start_time})
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>Number of Weeks</Label>
                <Select value={recurringWeeks.toString()} onValueChange={(value) => setRecurringWeeks(Number(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3, 4, 5, 6, 8, 10, 12].map(weeks => (
                      <SelectItem key={weeks} value={weeks.toString()}>{weeks} weeks</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <Button onClick={handleCreateRecurring} disabled={!selectedEntry}>
                Create Recurring Entries
              </Button>
            </div>
          )}

          {/* Template */}
          {operationType === 'template' && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Module</Label>
                  <Select value={templateData.module_id} onValueChange={(value) => setTemplateData({...templateData, module_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select module" />
                    </SelectTrigger>
                    <SelectContent>
                      {modules.map(module => (
                        <SelectItem key={module.id} value={module.id}>{module.module_name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Lecturer</Label>
                  <Select value={templateData.lecturer_id} onValueChange={(value) => setTemplateData({...templateData, lecturer_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select lecturer" />
                    </SelectTrigger>
                    <SelectContent>
                      {lecturers.map(lecturer => (
                        <SelectItem key={lecturer.id} value={lecturer.id}>{lecturer.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Classroom</Label>
                  <Select value={templateData.classroom_id} onValueChange={(value) => setTemplateData({...templateData, classroom_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select classroom" />
                    </SelectTrigger>
                    <SelectContent>
                      {classrooms.map(classroom => (
                        <SelectItem key={classroom.id} value={classroom.id}>{classroom.room_number}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Class Group</Label>
                  <Select value={templateData.group_id} onValueChange={(value) => setTemplateData({...templateData, group_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select group" />
                    </SelectTrigger>
                    <SelectContent>
                      {classGroups.map(group => (
                        <SelectItem key={group.id} value={group.id}>{group.program_name} - Year {group.year_level}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label>Days</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {DAYS.map(day => (
                    <div key={day} className="flex items-center space-x-2">
                      <Checkbox
                        id={`template-${day}`}
                        checked={targetDays.includes(day)}
                        onCheckedChange={() => handleDayToggle(day)}
                      />
                      <Label htmlFor={`template-${day}`} className="text-sm">{day}</Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <Label>Time Slots</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {getAvailableTimeSlots().map(timeSlot => (
                    <div key={timeSlot} className="flex items-center space-x-2">
                      <Checkbox
                        id={`time-${timeSlot}`}
                        checked={selectedTimeSlots.includes(timeSlot)}
                        onCheckedChange={() => handleTimeSlotToggle(timeSlot)}
                      />
                      <Label htmlFor={`time-${timeSlot}`} className="text-sm">{timeSlot}</Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_online"
                  checked={templateData.is_online}
                  onCheckedChange={(checked) => setTemplateData({...templateData, is_online: !!checked})}
                />
                <Label htmlFor="is_online">Online Class</Label>
              </div>
              
              <Button 
                onClick={handleCreateFromTemplate} 
                disabled={!templateData.module_id || !templateData.lecturer_id || !templateData.classroom_id || !templateData.group_id || targetDays.length === 0 || selectedTimeSlots.length === 0}
              >
                Create from Template
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
