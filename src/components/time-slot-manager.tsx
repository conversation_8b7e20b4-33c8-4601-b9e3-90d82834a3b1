"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Clock, Trash2, Edit } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface TimeSlot {
  id: string;
  start_time: string;
  end_time: string;
  duration: number; // in minutes
  is_break?: boolean;
  label?: string;
}

interface TimeSlotManagerProps {
  timeSlots: TimeSlot[];
  onTimeSlotsChange: (timeSlots: TimeSlot[]) => void;
  occupiedSlots?: Set<string>; // Set of time slot IDs that are occupied
}

const DEFAULT_TIME_SLOTS: TimeSlot[] = [
  { id: '1', start_time: '09:00', end_time: '10:00', duration: 60 },
  { id: '2', start_time: '10:00', end_time: '11:00', duration: 60 },
  { id: '3', start_time: '11:00', end_time: '12:00', duration: 60 },
  { id: '4', start_time: '12:00', end_time: '13:00', duration: 60, is_break: true, label: 'Lunch Break' },
  { id: '5', start_time: '13:00', end_time: '14:00', duration: 60 },
  { id: '6', start_time: '14:00', end_time: '15:00', duration: 60 },
  { id: '7', start_time: '15:00', end_time: '16:00', duration: 60 },
  { id: '8', start_time: '16:00', end_time: '17:00', duration: 60 },
  { id: '9', start_time: '17:00', end_time: '18:00', duration: 60 },
];

export default function TimeSlotManager({ 
  timeSlots = DEFAULT_TIME_SLOTS, 
  onTimeSlotsChange,
  occupiedSlots = new Set()
}: TimeSlotManagerProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSlot, setEditingSlot] = useState<TimeSlot | null>(null);
  const [newSlot, setNewSlot] = useState({
    start_time: '',
    end_time: '',
    is_break: false,
    label: ''
  });

  const calculateDuration = (start: string, end: string): number => {
    const [startHour, startMin] = start.split(':').map(Number);
    const [endHour, endMin] = end.split(':').map(Number);
    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;
    return endMinutes - startMinutes;
  };

  const handleAddSlot = () => {
    if (!newSlot.start_time || !newSlot.end_time) return;
    
    const duration = calculateDuration(newSlot.start_time, newSlot.end_time);
    if (duration <= 0) return;

    const slot: TimeSlot = {
      id: Date.now().toString(),
      start_time: newSlot.start_time,
      end_time: newSlot.end_time,
      duration,
      is_break: newSlot.is_break,
      label: newSlot.label || undefined
    };

    const updatedSlots = [...timeSlots, slot].sort((a, b) => 
      a.start_time.localeCompare(b.start_time)
    );
    
    onTimeSlotsChange(updatedSlots);
    setNewSlot({ start_time: '', end_time: '', is_break: false, label: '' });
    setIsDialogOpen(false);
  };

  const handleEditSlot = (slot: TimeSlot) => {
    setEditingSlot(slot);
    setNewSlot({
      start_time: slot.start_time,
      end_time: slot.end_time,
      is_break: slot.is_break || false,
      label: slot.label || ''
    });
    setIsDialogOpen(true);
  };

  const handleUpdateSlot = () => {
    if (!editingSlot || !newSlot.start_time || !newSlot.end_time) return;
    
    const duration = calculateDuration(newSlot.start_time, newSlot.end_time);
    if (duration <= 0) return;

    const updatedSlot: TimeSlot = {
      ...editingSlot,
      start_time: newSlot.start_time,
      end_time: newSlot.end_time,
      duration,
      is_break: newSlot.is_break,
      label: newSlot.label || undefined
    };

    const updatedSlots = timeSlots.map(slot => 
      slot.id === editingSlot.id ? updatedSlot : slot
    ).sort((a, b) => a.start_time.localeCompare(b.start_time));
    
    onTimeSlotsChange(updatedSlots);
    setEditingSlot(null);
    setNewSlot({ start_time: '', end_time: '', is_break: false, label: '' });
    setIsDialogOpen(false);
  };

  const handleDeleteSlot = (slotId: string) => {
    if (occupiedSlots.has(slotId)) {
      alert('Cannot delete a time slot that has scheduled entries. Please remove the entries first.');
      return;
    }
    
    const updatedSlots = timeSlots.filter(slot => slot.id !== slotId);
    onTimeSlotsChange(updatedSlots);
  };

  const resetToDefault = () => {
    onTimeSlotsChange(DEFAULT_TIME_SLOTS);
  };

  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 7; hour <= 22; hour++) {
      for (let min = 0; min < 60; min += 15) {
        const timeStr = `${hour.toString().padStart(2, '0')}:${min.toString().padStart(2, '0')}`;
        options.push(timeStr);
      }
    }
    return options;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Time Slot Management
          </CardTitle>
          <div className="flex gap-2">
            <Button onClick={resetToDefault} variant="outline" size="sm">
              Reset to Default
            </Button>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" onClick={() => {
                  setEditingSlot(null);
                  setNewSlot({ start_time: '', end_time: '', is_break: false, label: '' });
                }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Slot
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {editingSlot ? 'Edit Time Slot' : 'Add New Time Slot'}
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="start_time">Start Time</Label>
                      <select
                        id="start_time"
                        value={newSlot.start_time}
                        onChange={(e) => setNewSlot({ ...newSlot, start_time: e.target.value })}
                        className="w-full p-2 border rounded"
                      >
                        <option value="">Select start time</option>
                        {generateTimeOptions().map(time => (
                          <option key={time} value={time}>{time}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="end_time">End Time</Label>
                      <select
                        id="end_time"
                        value={newSlot.end_time}
                        onChange={(e) => setNewSlot({ ...newSlot, end_time: e.target.value })}
                        className="w-full p-2 border rounded"
                      >
                        <option value="">Select end time</option>
                        {generateTimeOptions().map(time => (
                          <option key={time} value={time}>{time}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="label">Label (optional)</Label>
                    <Input
                      id="label"
                      value={newSlot.label}
                      onChange={(e) => setNewSlot({ ...newSlot, label: e.target.value })}
                      placeholder="e.g., Lunch Break, Study Period"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_break"
                      checked={newSlot.is_break}
                      onChange={(e) => setNewSlot({ ...newSlot, is_break: e.target.checked })}
                    />
                    <Label htmlFor="is_break">Mark as break/non-teaching period</Label>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={editingSlot ? handleUpdateSlot : handleAddSlot}>
                      {editingSlot ? 'Update' : 'Add'} Slot
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {timeSlots.map((slot) => {
            const isOccupied = occupiedSlots.has(slot.id);
            return (
              <div
                key={slot.id}
                className={cn(
                  "flex items-center justify-between p-3 border rounded-lg",
                  slot.is_break && "bg-yellow-50 border-yellow-200",
                  isOccupied && "bg-blue-50 border-blue-200"
                )}
              >
                <div className="flex items-center gap-3">
                  <span className="font-mono text-sm">
                    {slot.start_time} - {slot.end_time}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    ({slot.duration} min)
                  </span>
                  {slot.label && (
                    <Badge variant="secondary">{slot.label}</Badge>
                  )}
                  {slot.is_break && (
                    <Badge variant="outline">Break</Badge>
                  )}
                  {isOccupied && (
                    <Badge variant="default">Occupied</Badge>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditSlot(slot)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteSlot(slot.id)}
                    disabled={isOccupied}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
