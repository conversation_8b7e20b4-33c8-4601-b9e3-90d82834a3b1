"use client";

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, XCircle, Clock, Users, MapPin, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AvailabilityStatus, ScheduleEntry, TimeRange } from '@/lib/types';
import { formatDuration } from '@/lib/time-utils';

interface AvailabilityIndicatorProps {
  status: AvailabilityStatus;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showText?: boolean;
}

export function AvailabilityIndicator({
  status,
  className,
  size = 'md',
  showIcon = true,
  showText = true
}: AvailabilityIndicatorProps) {
  const getStatusConfig = (status: AvailabilityStatus) => {
    switch (status) {
      case 'available':
        return {
          icon: CheckCircle,
          text: 'Available',
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 border-green-200'
        };
      case 'partially_available':
        return {
          icon: AlertCircle,
          text: 'Partially Available',
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
        };
      case 'busy':
        return {
          icon: Clock,
          text: 'Busy',
          variant: 'outline' as const,
          className: 'bg-orange-100 text-orange-800 border-orange-200'
        };
      case 'conflict':
        return {
          icon: XCircle,
          text: 'Conflict',
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 border-red-200'
        };
      default:
        return {
          icon: AlertCircle,
          text: 'Unknown',
          variant: 'outline' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200'
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;
  
  const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';
  const textSize = size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm';

  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, textSize, className)}
    >
      {showIcon && <Icon className={cn(iconSize, showText && 'mr-1')} />}
      {showText && config.text}
    </Badge>
  );
}

interface ResourceAvailabilityCardProps {
  resourceType: 'classroom' | 'lecturer' | 'group';
  resourceName: string;
  status: AvailabilityStatus;
  conflicts?: ScheduleEntry[];
  timeRange?: TimeRange;
  className?: string;
}

export function ResourceAvailabilityCard({
  resourceType,
  resourceName,
  status,
  conflicts = [],
  timeRange,
  className
}: ResourceAvailabilityCardProps) {
  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'classroom':
        return MapPin;
      case 'lecturer':
        return User;
      case 'group':
        return Users;
      default:
        return AlertCircle;
    }
  };

  const ResourceIcon = getResourceIcon(resourceType);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <ResourceIcon className="h-4 w-4" />
            <span className="capitalize">{resourceType}</span>
          </div>
          <AvailabilityIndicator status={status} size="sm" />
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <p className="font-medium text-sm">{resourceName}</p>
          
          {timeRange && (
            <p className="text-xs text-muted-foreground">
              {timeRange.start_time} - {timeRange.end_time} 
              ({formatDuration(timeRange.duration_minutes)})
            </p>
          )}
          
          {conflicts.length > 0 && (
            <Alert variant="destructive" className="py-2">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                {conflicts.length === 1 
                  ? `Conflict with existing class`
                  : `${conflicts.length} conflicts detected`
                }
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface OverallAvailabilityDisplayProps {
  overallStatus: AvailabilityStatus;
  classroomAvailability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
  lecturerAvailability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
  groupAvailability: { status: AvailabilityStatus; conflicts: ScheduleEntry[] };
  classroomName?: string;
  lecturerName?: string;
  groupName?: string;
  timeRange?: TimeRange;
  className?: string;
}

export function OverallAvailabilityDisplay({
  overallStatus,
  classroomAvailability,
  lecturerAvailability,
  groupAvailability,
  classroomName = "Selected Classroom",
  lecturerName = "Selected Lecturer",
  groupName = "Selected Group",
  timeRange,
  className
}: OverallAvailabilityDisplayProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Availability Check</h3>
        <AvailabilityIndicator status={overallStatus} size="lg" />
      </div>
      
      {timeRange && (
        <div className="text-sm text-muted-foreground">
          Checking availability for {timeRange.start_time} - {timeRange.end_time} 
          ({formatDuration(timeRange.duration_minutes)})
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <ResourceAvailabilityCard
          resourceType="classroom"
          resourceName={classroomName}
          status={classroomAvailability.status}
          conflicts={classroomAvailability.conflicts}
          timeRange={timeRange}
        />
        
        <ResourceAvailabilityCard
          resourceType="lecturer"
          resourceName={lecturerName}
          status={lecturerAvailability.status}
          conflicts={lecturerAvailability.conflicts}
          timeRange={timeRange}
        />
        
        <ResourceAvailabilityCard
          resourceType="group"
          resourceName={groupName}
          status={groupAvailability.status}
          conflicts={groupAvailability.conflicts}
          timeRange={timeRange}
        />
      </div>
      
      {overallStatus === 'conflict' && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            This time slot has conflicts. Please select a different time or resolve the conflicts.
          </AlertDescription>
        </Alert>
      )}
      
      {overallStatus === 'partially_available' && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            This time slot is available but may not be optimal based on preferences.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

interface TimeSlotAvailabilityProps {
  timeSlots: Array<{
    time: string;
    status: AvailabilityStatus;
    conflicts?: number;
  }>;
  onTimeSlotClick?: (time: string) => void;
  selectedTime?: string;
  className?: string;
}

export function TimeSlotAvailability({
  timeSlots,
  onTimeSlotClick,
  selectedTime,
  className
}: TimeSlotAvailabilityProps) {
  return (
    <div className={cn("grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-2", className)}>
      {timeSlots.map(({ time, status, conflicts }) => (
        <button
          key={time}
          onClick={() => onTimeSlotClick?.(time)}
          className={cn(
            "p-2 text-xs rounded border transition-colors",
            "hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring",
            selectedTime === time && "ring-2 ring-primary",
            status === 'available' && "bg-green-50 border-green-200 text-green-800",
            status === 'partially_available' && "bg-yellow-50 border-yellow-200 text-yellow-800",
            status === 'busy' && "bg-orange-50 border-orange-200 text-orange-800",
            status === 'conflict' && "bg-red-50 border-red-200 text-red-800"
          )}
          disabled={status === 'conflict'}
        >
          <div className="font-mono">{time}</div>
          {conflicts && conflicts > 0 && (
            <div className="text-xs mt-1">
              {conflicts} conflict{conflicts > 1 ? 's' : ''}
            </div>
          )}
        </button>
      ))}
    </div>
  );
}
