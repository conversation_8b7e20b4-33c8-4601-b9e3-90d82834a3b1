rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions for validation
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() &&
             request.auth.token.get('role', '') == 'admin';
    }

    // Check if running in emulator (for development)
    function isEmulator() {
      // In emulator, request.auth is null and we can detect this
      // This is a simple way to differentiate emulator from production
      return resource == null || request.auth == null;
    }

    // Allow operations in emulator or with proper authentication in production
    function canWrite() {
      return isEmulator() || isAuthenticated();
    }

    function canAdminWrite() {
      return isEmulator() || isAdmin();
    }

    function isValidString(field, minLength, maxLength) {
      return field is string &&
             field.size() >= minLength &&
             field.size() <= maxLength;
    }

    function isValidEmail(email) {
      return email is string && email.matches('.*@.*\\..*');
    }

    function hasRequiredFields(requiredFields) {
      return requiredFields.toSet().difference(resource.data.keys().toSet()).size() == 0;
    }

    // Classrooms collection
    match /classrooms/{classroomId} {
      allow read: if true; // Anyone can read classroom info for scheduling
      allow create, update, delete: if canWrite();
    }

    // Lecturers collection
    match /lecturers/{lecturerId} {
      allow read: if true; // Anyone can read lecturer info for scheduling
      allow create, update, delete: if canWrite();
    }

    // Modules collection
    match /modules/{moduleId} {
      allow read: if true; // Anyone can read module info for scheduling
      allow create, update, delete: if canWrite();
    }

    // Class Groups collection
    match /class_groups/{groupId} {
      allow read: if true; // Anyone can read group info for scheduling
      allow create, update, delete: if canWrite();
    }

    // Schedule Entries collection
    match /schedule_entries/{entryId} {
      allow read: if true; // Anyone can read schedule for viewing timetables
      allow create, update, delete: if canWrite(); // Allow writes in emulator or with auth in production
    }

    // User profiles collection (for future authentication)
    match /users/{userId} {
      allow read, write: if canWrite();
    }

    // System settings (admin only)
    match /settings/{settingId} {
      allow read: if true;
      allow write: if canAdminWrite();
    }

    // Audit logs (read-only for admins, write for system)
    match /audit_logs/{logId} {
      allow read: if canWrite();
      allow create: if canWrite();
    }
  }
}